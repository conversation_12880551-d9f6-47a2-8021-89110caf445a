<?php
/**
 * ESP Admin Class
 * 
 * Handles admin interface for the ESP Administration Manager
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class ESP_Admin {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'admin_init'));
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        // Main menu
        add_menu_page(
            __('ESP Administration', 'esp-admin-manager'),
            __('ESP Administration', 'esp-admin-manager'),
            'manage_options',
            'esp-admin-dashboard',
            array($this, 'dashboard_page'),
            'dashicons-building',
            30
        );
        
        // Dashboard submenu
        add_submenu_page(
            'esp-admin-dashboard',
            __('Dashboard', 'esp-admin-manager'),
            __('Dashboard', 'esp-admin-manager'),
            'manage_options',
            'esp-admin-dashboard',
            array($this, 'dashboard_page')
        );
        
        // Governor submenu
        add_submenu_page(
            'esp-admin-dashboard',
            __('Governor Profile', 'esp-admin-manager'),
            __('Governor Profile', 'esp-admin-manager'),
            'manage_options',
            'esp-admin-governor',
            array($this, 'governor_page')
        );
        
        // MPs submenu
        add_submenu_page(
            'esp-admin-dashboard',
            __('Members of Parliament', 'esp-admin-manager'),
            __('MPs', 'esp-admin-manager'),
            'manage_options',
            'esp-admin-mps',
            array($this, 'mps_page')
        );
        
        // Districts submenu
        add_submenu_page(
            'esp-admin-dashboard',
            __('Districts', 'esp-admin-manager'),
            __('Districts', 'esp-admin-manager'),
            'manage_options',
            'esp-admin-districts',
            array($this, 'districts_page')
        );
        
        // Statistics submenu
        add_submenu_page(
            'esp-admin-dashboard',
            __('Provincial Statistics', 'esp-admin-manager'),
            __('Statistics', 'esp-admin-manager'),
            'manage_options',
            'esp-admin-statistics',
            array($this, 'statistics_page')
        );
        
        // Administrative Structure submenu
        add_submenu_page(
            'esp-admin-dashboard',
            __('Administrative Structure', 'esp-admin-manager'),
            __('Admin Structure', 'esp-admin-manager'),
            'manage_options',
            'esp-admin-structure',
            array($this, 'structure_page')
        );
        
        // Events submenu
        add_submenu_page(
            'esp-admin-dashboard',
            __('Events', 'esp-admin-manager'),
            __('Events', 'esp-admin-manager'),
            'manage_options',
            'esp-admin-events',
            array($this, 'events_page')
        );
        
        // News submenu
        add_submenu_page(
            'esp-admin-dashboard',
            __('News', 'esp-admin-manager'),
            __('News', 'esp-admin-manager'),
            'manage_options',
            'esp-admin-news',
            array($this, 'news_page')
        );
        
        // Contact submenu
        add_submenu_page(
            'esp-admin-dashboard',
            __('Contact Information', 'esp-admin-manager'),
            __('Contact Info', 'esp-admin-manager'),
            'manage_options',
            'esp-admin-contact',
            array($this, 'contact_page')
        );
    }
    
    /**
     * Initialize admin settings
     */
    public function admin_init() {
        // Register settings
        register_setting('esp_statistics_group', 'esp_provincial_statistics');
        register_setting('esp_contact_group', 'esp_contact_information');
        register_setting('esp_structure_group', 'esp_administrative_structure');
    }
    
    /**
     * Dashboard page
     */
    public function dashboard_page() {
        // Show debug helper if requested
        if (isset($_GET['debug']) && $_GET['debug'] == '1' && current_user_can('manage_options')) {
            include ESP_ADMIN_MANAGER_PLUGIN_DIR . 'admin/esp-debug-helper.php';
            return;
        }

        include ESP_ADMIN_MANAGER_PLUGIN_DIR . 'admin/views/dashboard.php';
    }
    
    /**
     * Governor page
     */
    public function governor_page() {
        // Handle form submission
        if (isset($_POST['submit']) && wp_verify_nonce($_POST['esp_nonce'], 'esp_governor_nonce')) {
            $this->save_governor_data();
        }
        
        include ESP_ADMIN_MANAGER_PLUGIN_DIR . 'admin/views/governor.php';
    }
    
    /**
     * MPs page
     */
    public function mps_page() {
        include ESP_ADMIN_MANAGER_PLUGIN_DIR . 'admin/views/mps.php';
    }
    
    /**
     * Districts page
     */
    public function districts_page() {
        include ESP_ADMIN_MANAGER_PLUGIN_DIR . 'admin/views/districts.php';
    }
    
    /**
     * Statistics page
     */
    public function statistics_page() {
        // Handle form submission
        if (isset($_POST['submit']) && wp_verify_nonce($_POST['esp_nonce'], 'esp_statistics_nonce')) {
            $this->save_statistics_data();
        }
        
        include ESP_ADMIN_MANAGER_PLUGIN_DIR . 'admin/views/statistics.php';
    }
    
    /**
     * Administrative structure page
     */
    public function structure_page() {
        // Handle form submission
        if (isset($_POST['submit']) && wp_verify_nonce($_POST['esp_nonce'], 'esp_structure_nonce')) {
            $this->save_structure_data();
        }
        
        include ESP_ADMIN_MANAGER_PLUGIN_DIR . 'admin/views/admin-structure.php';
    }
    
    /**
     * Events page
     */
    public function events_page() {
        include ESP_ADMIN_MANAGER_PLUGIN_DIR . 'admin/views/events.php';
    }
    
    /**
     * News page
     */
    public function news_page() {
        include ESP_ADMIN_MANAGER_PLUGIN_DIR . 'admin/views/news.php';
    }
    
    /**
     * Contact page
     */
    public function contact_page() {
        // Handle form submission
        if (isset($_POST['submit']) && wp_verify_nonce($_POST['esp_nonce'], 'esp_contact_nonce')) {
            $this->save_contact_data();
        }
        
        include ESP_ADMIN_MANAGER_PLUGIN_DIR . 'admin/views/contact.php';
    }
    
    /**
     * Save governor data
     */
    private function save_governor_data() {
        // Get the governor post or create one
        $governors = get_posts(array(
            'post_type' => 'esp_governor',
            'numberposts' => 1
        ));

        if (!empty($governors)) {
            $governor_id = $governors[0]->ID;
        } else {
            // Create governor post if it doesn't exist
            $governor_id = wp_insert_post(array(
                'post_title' => sanitize_text_field($_POST['governor_name']),
                'post_content' => wp_kses_post($_POST['governor_message']),
                'post_status' => 'publish',
                'post_type' => 'esp_governor'
            ));
        }

        if ($governor_id) {
            // Update post
            wp_update_post(array(
                'ID' => $governor_id,
                'post_title' => sanitize_text_field($_POST['governor_name']),
                'post_content' => wp_kses_post($_POST['governor_message'])
            ));

            // Update meta fields
            update_post_meta($governor_id, '_esp_governor_title', sanitize_text_field($_POST['governor_title']));
            update_post_meta($governor_id, '_esp_governor_party', sanitize_text_field($_POST['governor_party']));
            update_post_meta($governor_id, '_esp_governor_email', sanitize_email($_POST['governor_email']));
            update_post_meta($governor_id, '_esp_governor_phone', sanitize_text_field($_POST['governor_phone']));

            // Update photo if provided
            if (!empty($_POST['governor_photo']) && is_numeric($_POST['governor_photo'])) {
                set_post_thumbnail($governor_id, intval($_POST['governor_photo']));
            }

            add_settings_error('esp_messages', 'esp_message', __('Governor profile updated successfully.', 'esp-admin-manager'), 'updated');
        }
    }
    
    /**
     * Save statistics data
     */
    private function save_statistics_data() {
        $statistics = array(
            'population' => sanitize_text_field($_POST['population']),
            'area' => sanitize_text_field($_POST['area']),
            'districts' => sanitize_text_field($_POST['districts']),
            'llgs' => sanitize_text_field($_POST['llgs']),
            'wards' => sanitize_text_field($_POST['wards']),
            'urban_llgs' => sanitize_text_field($_POST['urban_llgs'])
        );
        
        update_option('esp_provincial_statistics', $statistics);
        add_settings_error('esp_messages', 'esp_message', __('Provincial statistics updated successfully.', 'esp-admin-manager'), 'updated');
    }
    
    /**
     * Save structure data
     */
    private function save_structure_data() {
        $structure = array(
            'government_sectors' => wp_kses_post($_POST['government_sectors']),
            'administrative_divisions' => wp_kses_post($_POST['administrative_divisions'])
        );
        
        update_option('esp_administrative_structure', $structure);
        add_settings_error('esp_messages', 'esp_message', __('Administrative structure updated successfully.', 'esp-admin-manager'), 'updated');
    }
    
    /**
     * Save contact data
     */
    private function save_contact_data() {
        $contact = array(
            'address' => sanitize_textarea_field($_POST['address']),
            'phone' => sanitize_text_field($_POST['phone']),
            'fax' => sanitize_text_field($_POST['fax']),
            'emergency' => sanitize_text_field($_POST['emergency']),
            'email' => sanitize_email($_POST['email']),
            'admin_email' => sanitize_email($_POST['admin_email']),
            'website' => sanitize_url($_POST['website']),
            'office_hours' => sanitize_textarea_field($_POST['office_hours'])
        );
        
        update_option('esp_contact_information', $contact);
        add_settings_error('esp_messages', 'esp_message', __('Contact information updated successfully.', 'esp-admin-manager'), 'updated');
    }
}
