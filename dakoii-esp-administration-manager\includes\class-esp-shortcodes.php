<?php
/**
 * ESP Shortcodes Class
 * 
 * Handles shortcodes for frontend display
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class ESP_Shortcodes {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->register_shortcodes();
    }
    
    /**
     * Register all shortcodes
     */
    public function register_shortcodes() {
        add_shortcode('esp_governor', array($this, 'governor_shortcode'));
        add_shortcode('esp_mps', array($this, 'mps_shortcode'));
        add_shortcode('esp_districts', array($this, 'districts_shortcode'));
        add_shortcode('esp_statistics', array($this, 'statistics_shortcode'));
        add_shortcode('esp_admin_structure', array($this, 'admin_structure_shortcode'));
        add_shortcode('esp_events', array($this, 'events_shortcode'));
        add_shortcode('esp_news', array($this, 'news_shortcode'));
        add_shortcode('esp_contact', array($this, 'contact_shortcode'));
        add_shortcode('esp_slideshow', array($this, 'slideshow_shortcode'));
    }
    
    /**
     * Governor shortcode
     */
    public function governor_shortcode($atts) {
        $atts = shortcode_atts(array(
            'show_photo' => 'true',
            'show_message' => 'true'
        ), $atts);
        
        $governors = get_posts(array(
            'post_type' => 'esp_governor',
            'numberposts' => 1,
            'post_status' => 'publish'
        ));
        
        if (empty($governors)) {
            return '<p>' . __('No governor profile found.', 'esp-admin-manager') . '</p>';
        }
        
        $governor = $governors[0];
        $title = get_post_meta($governor->ID, '_esp_governor_title', true);
        $party = get_post_meta($governor->ID, '_esp_governor_party', true);
        
        ob_start();
        ?>
        <section class="esp-governor-section">
            <div class="esp-governor-grid">
                <?php if ($atts['show_photo'] === 'true'): ?>
                <div class="esp-governor-photo">
                    <?php
                    $photo_id = get_post_thumbnail_id($governor->ID);
                    $photo_url = $photo_id ? wp_get_attachment_image_url($photo_id, 'medium') : '';
                    ?>
                    <?php if ($photo_url): ?>
                        <img src="<?php echo esc_url($photo_url); ?>" alt="<?php echo esc_attr($governor->post_title); ?>" />
                    <?php else: ?>
                        <div class="esp-placeholder-photo">👤</div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
                
                <div class="esp-governor-content">
                    <h2><?php echo esc_html($governor->post_title); ?></h2>
                    <?php if ($title): ?>
                        <div class="esp-governor-title"><?php echo esc_html($title); ?></div>
                    <?php endif; ?>
                    <?php if ($party): ?>
                        <div class="esp-governor-party"><?php echo esc_html($party); ?></div>
                    <?php endif; ?>
                    
                    <?php if ($atts['show_message'] === 'true' && $governor->post_content): ?>
                        <div class="esp-governor-message">
                            <?php echo wp_kses_post($governor->post_content); ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </section>
        <?php
        return ob_get_clean();
    }
    
    /**
     * MPs shortcode
     */
    public function mps_shortcode($atts) {
        $atts = shortcode_atts(array(
            'limit' => -1,
            'show_photos' => 'true'
        ), $atts);
        
        $mps = get_posts(array(
            'post_type' => 'esp_mp',
            'numberposts' => intval($atts['limit']),
            'post_status' => 'publish',
            'orderby' => 'title',
            'order' => 'ASC'
        ));
        
        if (empty($mps)) {
            return '<p>' . __('No MPs found.', 'esp-admin-manager') . '</p>';
        }
        
        ob_start();
        ?>
        <section class="esp-parliament-section">
            <div class="esp-mp-grid">
                <?php foreach ($mps as $mp): 
                    $electorate = get_post_meta($mp->ID, '_esp_mp_electorate', true);
                    $party = get_post_meta($mp->ID, '_esp_mp_party', true);
                ?>
                    <div class="esp-mp-card">
                        <?php if ($atts['show_photos'] === 'true'): ?>
                            <div class="esp-mp-photo">
                                <?php if (has_post_thumbnail($mp->ID)): ?>
                                    <?php echo get_the_post_thumbnail($mp->ID, 'thumbnail'); ?>
                                <?php else: ?>
                                    👤
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                        
                        <div class="esp-mp-name"><?php echo esc_html($mp->post_title); ?></div>
                        
                        <?php if ($electorate): ?>
                            <div class="esp-mp-electorate"><?php echo esc_html($electorate); ?></div>
                        <?php endif; ?>
                        
                        <?php if ($party): ?>
                            <div class="esp-mp-party"><?php echo esc_html($party); ?></div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        </section>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Districts shortcode
     */
    public function districts_shortcode($atts) {
        $atts = shortcode_atts(array(
            'limit' => -1,
            'show_stats' => 'true'
        ), $atts);
        
        $districts = get_posts(array(
            'post_type' => 'esp_district',
            'numberposts' => intval($atts['limit']),
            'post_status' => 'publish',
            'orderby' => 'title',
            'order' => 'ASC'
        ));
        
        if (empty($districts)) {
            return '<p>' . __('No districts found.', 'esp-admin-manager') . '</p>';
        }
        
        ob_start();
        ?>
        <section class="esp-districts-section">
            <div class="esp-districts-grid">
                <?php foreach ($districts as $district): 
                    $llgs = get_post_meta($district->ID, '_esp_district_llgs', true);
                    $wards = get_post_meta($district->ID, '_esp_district_wards', true);
                ?>
                    <div class="esp-district-card">
                        <div class="esp-district-name"><?php echo esc_html($district->post_title); ?></div>
                        
                        <?php if ($atts['show_stats'] === 'true' && ($llgs || $wards)): ?>
                            <div class="esp-district-stats">
                                <?php if ($llgs): ?>
                                    <div class="esp-district-stat">
                                        <div class="esp-district-stat-number"><?php echo esc_html($llgs); ?></div>
                                        <div class="esp-district-stat-label"><?php _e('LLGs', 'esp-admin-manager'); ?></div>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($wards): ?>
                                    <div class="esp-district-stat">
                                        <div class="esp-district-stat-number"><?php echo esc_html($wards); ?></div>
                                        <div class="esp-district-stat-label"><?php _e('Wards', 'esp-admin-manager'); ?></div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($district->post_content): ?>
                            <div class="esp-district-description">
                                <?php echo wp_kses_post($district->post_content); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        </section>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Statistics shortcode
     */
    public function statistics_shortcode($atts) {
        $atts = shortcode_atts(array(
            'layout' => 'grid' // grid or list
        ), $atts);
        
        $statistics = get_option('esp_provincial_statistics', array());
        
        if (empty($statistics)) {
            return '<p>' . __('No provincial statistics found.', 'esp-admin-manager') . '</p>';
        }
        
        ob_start();
        ?>
        <section class="esp-statistics-section">
            <div class="esp-map-stats <?php echo esc_attr($atts['layout']); ?>">
                <?php if (isset($statistics['population'])): ?>
                    <div class="esp-map-stat">
                        <div class="esp-map-stat-number"><?php echo esc_html($statistics['population']); ?></div>
                        <div class="esp-map-stat-label"><?php _e('Population', 'esp-admin-manager'); ?></div>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($statistics['area'])): ?>
                    <div class="esp-map-stat">
                        <div class="esp-map-stat-number"><?php echo esc_html($statistics['area']); ?></div>
                        <div class="esp-map-stat-label"><?php _e('Area (km²)', 'esp-admin-manager'); ?></div>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($statistics['districts'])): ?>
                    <div class="esp-map-stat">
                        <div class="esp-map-stat-number"><?php echo esc_html($statistics['districts']); ?></div>
                        <div class="esp-map-stat-label"><?php _e('Districts', 'esp-admin-manager'); ?></div>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($statistics['llgs'])): ?>
                    <div class="esp-map-stat">
                        <div class="esp-map-stat-number"><?php echo esc_html($statistics['llgs']); ?></div>
                        <div class="esp-map-stat-label"><?php _e('LLGs', 'esp-admin-manager'); ?></div>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($statistics['wards'])): ?>
                    <div class="esp-map-stat">
                        <div class="esp-map-stat-number"><?php echo esc_html($statistics['wards']); ?></div>
                        <div class="esp-map-stat-label"><?php _e('Wards', 'esp-admin-manager'); ?></div>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($statistics['urban_llgs'])): ?>
                    <div class="esp-map-stat">
                        <div class="esp-map-stat-number"><?php echo esc_html($statistics['urban_llgs']); ?></div>
                        <div class="esp-map-stat-label"><?php _e('Urban LLGs', 'esp-admin-manager'); ?></div>
                    </div>
                <?php endif; ?>
            </div>
        </section>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Events shortcode
     */
    public function events_shortcode($atts) {
        $atts = shortcode_atts(array(
            'limit' => 5,
            'upcoming_only' => 'true'
        ), $atts);
        
        $args = array(
            'post_type' => 'esp_event',
            'numberposts' => intval($atts['limit']),
            'post_status' => 'publish',
            'orderby' => 'meta_value',
            'meta_key' => '_esp_event_start_date',
            'order' => 'ASC'
        );
        
        if ($atts['upcoming_only'] === 'true') {
            $args['meta_query'] = array(
                array(
                    'key' => '_esp_event_start_date',
                    'value' => date('Y-m-d'),
                    'compare' => '>='
                )
            );
        }
        
        $events = get_posts($args);
        
        if (empty($events)) {
            return '<p>' . __('No events found.', 'esp-admin-manager') . '</p>';
        }
        
        ob_start();
        ?>
        <section class="esp-events-section">
            <div class="esp-featured-events">
                <?php foreach ($events as $event): 
                    $start_date = get_post_meta($event->ID, '_esp_event_start_date', true);
                    $end_date = get_post_meta($event->ID, '_esp_event_end_date', true);
                    $location = get_post_meta($event->ID, '_esp_event_location', true);
                ?>
                    <div class="esp-event-item">
                        <div class="esp-event-date">
                            <?php 
                            if ($start_date) {
                                $formatted_start = date('F j, Y', strtotime($start_date));
                                if ($end_date && $end_date !== $start_date) {
                                    $formatted_end = date('F j, Y', strtotime($end_date));
                                    echo esc_html($formatted_start . ' - ' . $formatted_end);
                                } else {
                                    echo esc_html($formatted_start);
                                }
                            }
                            ?>
                        </div>
                        <div class="esp-event-title"><?php echo esc_html($event->post_title); ?></div>
                        <?php if ($location): ?>
                            <div class="esp-event-location"><?php echo esc_html($location); ?></div>
                        <?php endif; ?>
                        <?php if ($event->post_content): ?>
                            <div class="esp-event-description"><?php echo wp_kses_post($event->post_content); ?></div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        </section>
        <?php
        return ob_get_clean();
    }
    
    /**
     * News shortcode
     */
    public function news_shortcode($atts) {
        $atts = shortcode_atts(array(
            'limit' => 5,
            'featured_only' => 'false'
        ), $atts);
        
        $args = array(
            'post_type' => 'esp_news',
            'numberposts' => intval($atts['limit']),
            'post_status' => 'publish',
            'orderby' => 'date',
            'order' => 'DESC'
        );
        
        if ($atts['featured_only'] === 'true') {
            $args['meta_query'] = array(
                array(
                    'key' => '_esp_news_featured',
                    'value' => '1',
                    'compare' => '='
                )
            );
        }
        
        $news = get_posts($args);
        
        if (empty($news)) {
            return '<p>' . __('No news found.', 'esp-admin-manager') . '</p>';
        }
        
        ob_start();
        ?>
        <section class="esp-news-section">
            <div class="esp-featured-news">
                <?php foreach ($news as $news_item): 
                    $news_date = get_post_meta($news_item->ID, '_esp_news_date', true);
                    $source = get_post_meta($news_item->ID, '_esp_news_source', true);
                ?>
                    <div class="esp-news-item">
                        <div class="esp-news-date">
                            <?php 
                            if ($news_date) {
                                echo esc_html(date('F j, Y', strtotime($news_date)));
                            } else {
                                echo esc_html(get_the_date('F j, Y', $news_item));
                            }
                            ?>
                        </div>
                        <div class="esp-news-title"><?php echo esc_html($news_item->post_title); ?></div>
                        <?php if ($source): ?>
                            <div class="esp-news-source"><?php echo esc_html($source); ?></div>
                        <?php endif; ?>
                        <div class="esp-news-excerpt">
                            <?php 
                            if ($news_item->post_excerpt) {
                                echo wp_kses_post($news_item->post_excerpt);
                            } else {
                                echo wp_kses_post(wp_trim_words($news_item->post_content, 30));
                            }
                            ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </section>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Contact shortcode
     */
    public function contact_shortcode($atts) {
        $atts = shortcode_atts(array(
            'layout' => 'grid' // grid or list
        ), $atts);
        
        $contact = get_option('esp_contact_information', array());
        
        if (empty($contact)) {
            return '<p>' . __('No contact information found.', 'esp-admin-manager') . '</p>';
        }
        
        ob_start();
        ?>
        <section class="esp-contact-section">
            <div class="esp-contact-grid <?php echo esc_attr($atts['layout']); ?>">
                <?php if (isset($contact['address'])): ?>
                    <div class="esp-contact-card">
                        <div class="esp-contact-icon">🏛️</div>
                        <h3 class="esp-contact-title"><?php _e('Provincial Headquarters', 'esp-admin-manager'); ?></h3>
                        <div class="esp-contact-info"><?php echo nl2br(esc_html($contact['address'])); ?></div>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($contact['phone']) || isset($contact['fax']) || isset($contact['emergency'])): ?>
                    <div class="esp-contact-card">
                        <div class="esp-contact-icon">📞</div>
                        <h3 class="esp-contact-title"><?php _e('Phone & Fax', 'esp-admin-manager'); ?></h3>
                        <div class="esp-contact-info">
                            <?php if (isset($contact['phone'])): ?>
                                <?php _e('Phone:', 'esp-admin-manager'); ?> <?php echo esc_html($contact['phone']); ?><br>
                            <?php endif; ?>
                            <?php if (isset($contact['fax'])): ?>
                                <?php _e('Fax:', 'esp-admin-manager'); ?> <?php echo esc_html($contact['fax']); ?><br>
                            <?php endif; ?>
                            <?php if (isset($contact['emergency'])): ?>
                                <?php _e('Emergency:', 'esp-admin-manager'); ?> <?php echo esc_html($contact['emergency']); ?>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($contact['email']) || isset($contact['admin_email']) || isset($contact['website'])): ?>
                    <div class="esp-contact-card">
                        <div class="esp-contact-icon">✉️</div>
                        <h3 class="esp-contact-title"><?php _e('Email & Web', 'esp-admin-manager'); ?></h3>
                        <div class="esp-contact-info">
                            <?php if (isset($contact['email'])): ?>
                                <a href="mailto:<?php echo esc_attr($contact['email']); ?>"><?php echo esc_html($contact['email']); ?></a><br>
                            <?php endif; ?>
                            <?php if (isset($contact['admin_email'])): ?>
                                <a href="mailto:<?php echo esc_attr($contact['admin_email']); ?>"><?php echo esc_html($contact['admin_email']); ?></a><br>
                            <?php endif; ?>
                            <?php if (isset($contact['website'])): ?>
                                <a href="<?php echo esc_url('http://' . $contact['website']); ?>" target="_blank"><?php echo esc_html($contact['website']); ?></a>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($contact['office_hours'])): ?>
                    <div class="esp-contact-card">
                        <div class="esp-contact-icon">🕐</div>
                        <h3 class="esp-contact-title"><?php _e('Office Hours', 'esp-admin-manager'); ?></h3>
                        <div class="esp-contact-info"><?php echo nl2br(esc_html($contact['office_hours'])); ?></div>
                    </div>
                <?php endif; ?>
            </div>
        </section>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Slideshow shortcode
     */
    public function slideshow_shortcode($atts) {
        $atts = shortcode_atts(array(
            'slides' => '3',
            'auto_play' => 'true',
            'interval' => '5000'
        ), $atts);
        
        ob_start();
        ?>
        <section class="esp-slideshow-section">
            <div class="esp-slideshow-container">
                <div class="esp-slide esp-slide-1 active">
                    <div class="esp-slide-content">
                        <h1 class="esp-slide-title"><?php _e('Welcome to East Sepik Province', 'esp-admin-manager'); ?></h1>
                        <p class="esp-slide-subtitle"><?php _e('The Heart of Papua New Guinea\'s Cultural Heritage', 'esp-admin-manager'); ?></p>
                    </div>
                </div>
                <div class="esp-slide esp-slide-2">
                    <div class="esp-slide-content">
                        <h1 class="esp-slide-title"><?php _e('Serving Our Communities', 'esp-admin-manager'); ?></h1>
                        <p class="esp-slide-subtitle"><?php _e('Committed to Development and Progress', 'esp-admin-manager'); ?></p>
                    </div>
                </div>
                <div class="esp-slide esp-slide-3">
                    <div class="esp-slide-content">
                        <h1 class="esp-slide-title"><?php _e('Building a Better Future', 'esp-admin-manager'); ?></h1>
                        <p class="esp-slide-subtitle"><?php _e('Together We Grow Stronger', 'esp-admin-manager'); ?></p>
                    </div>
                </div>
            </div>
            <div class="esp-slideshow-nav">
                <span class="esp-slide-dot active" onclick="espCurrentSlide(1)"></span>
                <span class="esp-slide-dot" onclick="espCurrentSlide(2)"></span>
                <span class="esp-slide-dot" onclick="espCurrentSlide(3)"></span>
            </div>
        </section>
        <?php
        return ob_get_clean();
    }
}
