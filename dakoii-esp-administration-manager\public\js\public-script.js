/**
 * ESP Administration Manager - Public JavaScript
 */

(function($) {
    'use strict';

    // Slideshow functionality
    let espSlideIndex = 1;
    
    function espShowSlides(n) {
        let slides = $('.esp-slide');
        let dots = $('.esp-slide-dot');
        
        if (n > slides.length) { espSlideIndex = 1; }
        if (n < 1) { espSlideIndex = slides.length; }
        
        slides.removeClass('active');
        dots.removeClass('active');
        
        if (slides.length > 0) {
            slides.eq(espSlideIndex - 1).addClass('active');
            dots.eq(espSlideIndex - 1).addClass('active');
        }
    }
    
    // Global function for slideshow navigation
    window.espCurrentSlide = function(n) {
        espShowSlides(espSlideIndex = n);
    };
    
    // Auto slideshow
    function espAutoSlides() {
        espSlideIndex++;
        espShowSlides(espSlideIndex);
    }
    
    // Initialize slideshow
    function initSlideshow() {
        if ($('.esp-slideshow-section').length > 0) {
            espShowSlides(espSlideIndex);
            
            // Auto-advance slides every 5 seconds
            setInterval(espAutoSlides, 5000);
            
            // Keyboard navigation
            $(document).on('keydown', function(e) {
                if ($('.esp-slideshow-section').is(':visible')) {
                    if (e.keyCode === 37) { // Left arrow
                        espCurrentSlide(espSlideIndex - 1);
                    } else if (e.keyCode === 39) { // Right arrow
                        espCurrentSlide(espSlideIndex + 1);
                    }
                }
            });
        }
    }
    
    // Smooth scrolling for anchor links
    function initSmoothScrolling() {
        $('a[href^="#"]').on('click', function(e) {
            e.preventDefault();
            
            let target = $(this.getAttribute('href'));
            if (target.length) {
                $('html, body').animate({
                    scrollTop: target.offset().top - 100
                }, 800);
            }
        });
    }
    
    // Animation on scroll
    function initScrollAnimations() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    $(entry.target).addClass('animate-in');
                }
            });
        }, observerOptions);
        
        // Observe elements for animation
        $('.esp-mp-card, .esp-district-card, .esp-contact-card').each(function() {
            observer.observe(this);
        });
    }
    
    // Lazy loading for images
    function initLazyLoading() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });
            
            $('.esp-lazy-image').each(function() {
                imageObserver.observe(this);
            });
        }
    }
    
    // Statistics counter animation
    function initCounterAnimation() {
        $('.esp-map-stat-number').each(function() {
            const $this = $(this);
            const countTo = parseInt($this.text().replace(/,/g, ''));
            
            if (!isNaN(countTo)) {
                $({ countNum: 0 }).animate({
                    countNum: countTo
                }, {
                    duration: 2000,
                    easing: 'swing',
                    step: function() {
                        $this.text(Math.floor(this.countNum).toLocaleString());
                    },
                    complete: function() {
                        $this.text(countTo.toLocaleString());
                    }
                });
            }
        });
    }
    
    // Responsive table handling
    function initResponsiveTables() {
        $('.esp-list-table').each(function() {
            if (!$(this).parent().hasClass('table-responsive')) {
                $(this).wrap('<div class="table-responsive"></div>');
            }
        });
    }
    
    // Card hover effects
    function initCardEffects() {
        $('.esp-mp-card, .esp-district-card').hover(
            function() {
                $(this).addClass('hover-effect');
            },
            function() {
                $(this).removeClass('hover-effect');
            }
        );
    }
    
    // Search functionality for lists
    function initSearchFunctionality() {
        // Add search box if there are many items
        if ($('.esp-mp-card').length > 6) {
            const searchBox = $('<div class="esp-search-box"><input type="text" placeholder="Search MPs..." class="esp-search-input"></div>');
            $('.esp-mp-grid').before(searchBox);
            
            $('.esp-search-input').on('keyup', function() {
                const value = $(this).val().toLowerCase();
                $('.esp-mp-card').filter(function() {
                    $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
                });
            });
        }
        
        if ($('.esp-district-card').length > 6) {
            const searchBox = $('<div class="esp-search-box"><input type="text" placeholder="Search Districts..." class="esp-search-input"></div>');
            $('.esp-districts-grid').before(searchBox);
            
            $('.esp-search-input').on('keyup', function() {
                const value = $(this).val().toLowerCase();
                $('.esp-district-card').filter(function() {
                    $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
                });
            });
        }
    }
    
    // Print functionality
    function initPrintFunctionality() {
        // Add print button to sections
        $('.esp-governor-section, .esp-parliament-section, .esp-districts-section').each(function() {
            const printBtn = $('<button class="esp-print-btn">🖨️ Print</button>');
            $(this).prepend(printBtn);
            
            printBtn.on('click', function() {
                const section = $(this).parent();
                const printWindow = window.open('', '_blank');
                printWindow.document.write(`
                    <html>
                        <head>
                            <title>East Sepik Provincial Administration</title>
                            <style>
                                body { font-family: Arial, sans-serif; margin: 20px; }
                                .esp-print-btn { display: none; }
                                ${$('style').html()}
                            </style>
                        </head>
                        <body>
                            ${section.html()}
                        </body>
                    </html>
                `);
                printWindow.document.close();
                printWindow.print();
            });
        });
    }
    
    // Accessibility improvements
    function initAccessibility() {
        // Add ARIA labels
        $('.esp-slide-dot').attr('role', 'button').attr('aria-label', 'Go to slide');
        $('.esp-mp-card').attr('role', 'article');
        $('.esp-district-card').attr('role', 'article');
        
        // Keyboard navigation for cards
        $('.esp-mp-card, .esp-district-card').attr('tabindex', '0').on('keydown', function(e) {
            if (e.keyCode === 13 || e.keyCode === 32) { // Enter or Space
                $(this).click();
            }
        });
        
        // Focus management for slideshow
        $('.esp-slide-dot').on('focus', function() {
            $(this).addClass('focused');
        }).on('blur', function() {
            $(this).removeClass('focused');
        });
    }
    
    // Error handling for missing images
    function initImageErrorHandling() {
        $('img').on('error', function() {
            const $this = $(this);
            if ($this.closest('.esp-governor-photo').length) {
                $this.parent().html('<div class="esp-placeholder-photo">👤</div>');
            } else if ($this.closest('.esp-mp-photo').length) {
                $this.parent().html('👤');
            }
        });
    }
    
    // Initialize all functionality when document is ready
    $(document).ready(function() {
        initSlideshow();
        initSmoothScrolling();
        initScrollAnimations();
        initLazyLoading();
        initResponsiveTables();
        initCardEffects();
        initSearchFunctionality();
        initPrintFunctionality();
        initAccessibility();
        initImageErrorHandling();
        
        // Trigger counter animation when statistics section is visible
        const statsObserver = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    initCounterAnimation();
                    statsObserver.unobserve(entry.target);
                }
            });
        });
        
        if ($('.esp-statistics-section').length) {
            statsObserver.observe($('.esp-statistics-section')[0]);
        }
    });
    
    // Handle window resize
    $(window).on('resize', function() {
        // Recalculate slideshow dimensions if needed
        if ($('.esp-slideshow-section').length > 0) {
            // Slideshow responsive adjustments
        }
    });
    
    // Handle window load
    $(window).on('load', function() {
        // Add loading animations
        $('.esp-mp-card, .esp-district-card, .esp-event-item, .esp-news-item').each(function(index) {
            $(this).css('animation-delay', (index * 0.1) + 's');
        });
    });

})(jQuery);
