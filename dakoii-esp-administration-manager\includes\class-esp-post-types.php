<?php
/**
 * ESP Post Types Class
 * 
 * Handles registration of custom post types for the ESP Administration Manager
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class ESP_Post_Types {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        add_action('init', array($this, 'register_post_types'));
        add_action('admin_init', array($this, 'maybe_flush_rewrite_rules'));
    }
    
    /**
     * Register all custom post types
     */
    public function register_post_types() {
        $this->register_governor_post_type();
        $this->register_mp_post_type();
        $this->register_district_post_type();
        $this->register_event_post_type();
        $this->register_news_post_type();
    }
    
    /**
     * Register Governor post type
     */
    private function register_governor_post_type() {
        $labels = array(
            'name'                  => _x('Governor', 'Post type general name', 'esp-admin-manager'),
            'singular_name'         => _x('Governor', 'Post type singular name', 'esp-admin-manager'),
            'menu_name'             => _x('Governor', 'Admin Menu text', 'esp-admin-manager'),
            'name_admin_bar'        => _x('Governor', 'Add New on Toolbar', 'esp-admin-manager'),
            'add_new'               => __('Add New', 'esp-admin-manager'),
            'add_new_item'          => __('Add New Governor', 'esp-admin-manager'),
            'new_item'              => __('New Governor', 'esp-admin-manager'),
            'edit_item'             => __('Edit Governor', 'esp-admin-manager'),
            'view_item'             => __('View Governor', 'esp-admin-manager'),
            'all_items'             => __('All Governors', 'esp-admin-manager'),
            'search_items'          => __('Search Governors', 'esp-admin-manager'),
            'not_found'             => __('No governors found.', 'esp-admin-manager'),
            'not_found_in_trash'    => __('No governors found in Trash.', 'esp-admin-manager'),
        );

        $args = array(
            'labels'             => $labels,
            'public'             => false,
            'publicly_queryable' => false,
            'show_ui'            => true,
            'show_in_menu'       => 'esp-admin-dashboard', // Show under our custom menu
            'show_in_admin_bar'  => true,
            'query_var'          => true,
            'rewrite'            => array('slug' => 'governor'),
            'capability_type'    => 'post',
            'capabilities'       => array(
                'edit_post'          => 'manage_options',
                'read_post'          => 'manage_options',
                'delete_post'        => 'manage_options',
                'edit_posts'         => 'manage_options',
                'edit_others_posts'  => 'manage_options',
                'publish_posts'      => 'manage_options',
                'read_private_posts' => 'manage_options',
            ),
            'has_archive'        => false,
            'hierarchical'       => false,
            'menu_position'      => null,
            'supports'           => array('title', 'editor', 'thumbnail'),
            'show_in_rest'       => false,
        );

        register_post_type('esp_governor', $args);
    }
    
    /**
     * Register MP post type
     */
    private function register_mp_post_type() {
        $labels = array(
            'name'                  => _x('Members of Parliament', 'Post type general name', 'esp-admin-manager'),
            'singular_name'         => _x('MP', 'Post type singular name', 'esp-admin-manager'),
            'menu_name'             => _x('MPs', 'Admin Menu text', 'esp-admin-manager'),
            'name_admin_bar'        => _x('MP', 'Add New on Toolbar', 'esp-admin-manager'),
            'add_new'               => __('Add New', 'esp-admin-manager'),
            'add_new_item'          => __('Add New MP', 'esp-admin-manager'),
            'new_item'              => __('New MP', 'esp-admin-manager'),
            'edit_item'             => __('Edit MP', 'esp-admin-manager'),
            'view_item'             => __('View MP', 'esp-admin-manager'),
            'all_items'             => __('All MPs', 'esp-admin-manager'),
            'search_items'          => __('Search MPs', 'esp-admin-manager'),
            'not_found'             => __('No MPs found.', 'esp-admin-manager'),
            'not_found_in_trash'    => __('No MPs found in Trash.', 'esp-admin-manager'),
        );

        $args = array(
            'labels'             => $labels,
            'public'             => false,
            'publicly_queryable' => false,
            'show_ui'            => true,
            'show_in_menu'       => 'esp-admin-dashboard', // Show under our custom menu
            'show_in_admin_bar'  => true,
            'query_var'          => true,
            'rewrite'            => array('slug' => 'mp'),
            'capability_type'    => 'post',
            'capabilities'       => array(
                'edit_post'          => 'manage_options',
                'read_post'          => 'manage_options',
                'delete_post'        => 'manage_options',
                'edit_posts'         => 'manage_options',
                'edit_others_posts'  => 'manage_options',
                'publish_posts'      => 'manage_options',
                'read_private_posts' => 'manage_options',
            ),
            'has_archive'        => false,
            'hierarchical'       => false,
            'menu_position'      => null,
            'supports'           => array('title', 'editor', 'thumbnail'),
            'show_in_rest'       => false,
        );

        register_post_type('esp_mp', $args);
    }
    
    /**
     * Register District post type
     */
    private function register_district_post_type() {
        $labels = array(
            'name'                  => _x('Districts', 'Post type general name', 'esp-admin-manager'),
            'singular_name'         => _x('District', 'Post type singular name', 'esp-admin-manager'),
            'menu_name'             => _x('Districts', 'Admin Menu text', 'esp-admin-manager'),
            'name_admin_bar'        => _x('District', 'Add New on Toolbar', 'esp-admin-manager'),
            'add_new'               => __('Add New', 'esp-admin-manager'),
            'add_new_item'          => __('Add New District', 'esp-admin-manager'),
            'new_item'              => __('New District', 'esp-admin-manager'),
            'edit_item'             => __('Edit District', 'esp-admin-manager'),
            'view_item'             => __('View District', 'esp-admin-manager'),
            'all_items'             => __('All Districts', 'esp-admin-manager'),
            'search_items'          => __('Search Districts', 'esp-admin-manager'),
            'not_found'             => __('No districts found.', 'esp-admin-manager'),
            'not_found_in_trash'    => __('No districts found in Trash.', 'esp-admin-manager'),
        );

        $args = array(
            'labels'             => $labels,
            'public'             => false,
            'publicly_queryable' => false,
            'show_ui'            => true,
            'show_in_menu'       => 'esp-admin-dashboard', // Show under our custom menu
            'show_in_admin_bar'  => true,
            'query_var'          => true,
            'rewrite'            => array('slug' => 'district'),
            'capability_type'    => 'post',
            'capabilities'       => array(
                'edit_post'          => 'manage_options',
                'read_post'          => 'manage_options',
                'delete_post'        => 'manage_options',
                'edit_posts'         => 'manage_options',
                'edit_others_posts'  => 'manage_options',
                'publish_posts'      => 'manage_options',
                'read_private_posts' => 'manage_options',
            ),
            'has_archive'        => false,
            'hierarchical'       => false,
            'menu_position'      => null,
            'supports'           => array('title', 'editor', 'thumbnail'),
            'show_in_rest'       => false,
        );

        register_post_type('esp_district', $args);
    }
    
    /**
     * Register Event post type
     */
    private function register_event_post_type() {
        $labels = array(
            'name'                  => _x('Events', 'Post type general name', 'esp-admin-manager'),
            'singular_name'         => _x('Event', 'Post type singular name', 'esp-admin-manager'),
            'menu_name'             => _x('Events', 'Admin Menu text', 'esp-admin-manager'),
            'name_admin_bar'        => _x('Event', 'Add New on Toolbar', 'esp-admin-manager'),
            'add_new'               => __('Add New', 'esp-admin-manager'),
            'add_new_item'          => __('Add New Event', 'esp-admin-manager'),
            'new_item'              => __('New Event', 'esp-admin-manager'),
            'edit_item'             => __('Edit Event', 'esp-admin-manager'),
            'view_item'             => __('View Event', 'esp-admin-manager'),
            'all_items'             => __('All Events', 'esp-admin-manager'),
            'search_items'          => __('Search Events', 'esp-admin-manager'),
            'not_found'             => __('No events found.', 'esp-admin-manager'),
            'not_found_in_trash'    => __('No events found in Trash.', 'esp-admin-manager'),
        );

        $args = array(
            'labels'             => $labels,
            'public'             => false,
            'publicly_queryable' => false,
            'show_ui'            => true,
            'show_in_menu'       => 'esp-admin-dashboard', // Show under our custom menu
            'show_in_admin_bar'  => true,
            'query_var'          => true,
            'rewrite'            => array('slug' => 'event'),
            'capability_type'    => 'post',
            'capabilities'       => array(
                'edit_post'          => 'manage_options',
                'read_post'          => 'manage_options',
                'delete_post'        => 'manage_options',
                'edit_posts'         => 'manage_options',
                'edit_others_posts'  => 'manage_options',
                'publish_posts'      => 'manage_options',
                'read_private_posts' => 'manage_options',
            ),
            'has_archive'        => false,
            'hierarchical'       => false,
            'menu_position'      => null,
            'supports'           => array('title', 'editor', 'thumbnail'),
            'show_in_rest'       => false,
        );

        register_post_type('esp_event', $args);
    }
    
    /**
     * Register News post type
     */
    private function register_news_post_type() {
        $labels = array(
            'name'                  => _x('News', 'Post type general name', 'esp-admin-manager'),
            'singular_name'         => _x('News', 'Post type singular name', 'esp-admin-manager'),
            'menu_name'             => _x('News', 'Admin Menu text', 'esp-admin-manager'),
            'name_admin_bar'        => _x('News', 'Add New on Toolbar', 'esp-admin-manager'),
            'add_new'               => __('Add New', 'esp-admin-manager'),
            'add_new_item'          => __('Add New News', 'esp-admin-manager'),
            'new_item'              => __('New News', 'esp-admin-manager'),
            'edit_item'             => __('Edit News', 'esp-admin-manager'),
            'view_item'             => __('View News', 'esp-admin-manager'),
            'all_items'             => __('All News', 'esp-admin-manager'),
            'search_items'          => __('Search News', 'esp-admin-manager'),
            'not_found'             => __('No news found.', 'esp-admin-manager'),
            'not_found_in_trash'    => __('No news found in Trash.', 'esp-admin-manager'),
        );

        $args = array(
            'labels'             => $labels,
            'public'             => false,
            'publicly_queryable' => false,
            'show_ui'            => true,
            'show_in_menu'       => 'esp-admin-dashboard', // Show under our custom menu
            'show_in_admin_bar'  => true,
            'query_var'          => true,
            'rewrite'            => array('slug' => 'news'),
            'capability_type'    => 'post',
            'capabilities'       => array(
                'edit_post'          => 'manage_options',
                'read_post'          => 'manage_options',
                'delete_post'        => 'manage_options',
                'edit_posts'         => 'manage_options',
                'edit_others_posts'  => 'manage_options',
                'publish_posts'      => 'manage_options',
                'read_private_posts' => 'manage_options',
            ),
            'has_archive'        => false,
            'hierarchical'       => false,
            'menu_position'      => null,
            'supports'           => array('title', 'editor', 'thumbnail', 'excerpt'),
            'show_in_rest'       => false,
        );

        register_post_type('esp_news', $args);
    }

    /**
     * Maybe flush rewrite rules if needed
     */
    public function maybe_flush_rewrite_rules() {
        if (get_option('esp_flush_rewrite_rules', false)) {
            flush_rewrite_rules();
            delete_option('esp_flush_rewrite_rules');
        }
    }

    /**
     * Force flush rewrite rules
     */
    public static function flush_rewrite_rules() {
        update_option('esp_flush_rewrite_rules', true);
    }
}
