<?php
/**
 * ESP Administration Manager - Contact Information View
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get current contact information
$contact = get_option('esp_contact_information', array());
?>

<div class="wrap">
    <div class="esp-admin-header">
        <h1><?php _e('Contact Information', 'esp-admin-manager'); ?></h1>
        <p><?php _e('Manage contact details for the East Sepik Provincial Administration', 'esp-admin-manager'); ?></p>
    </div>

    <?php settings_errors('esp_messages'); ?>

    <form method="post" action="">
        <?php wp_nonce_field('esp_contact_nonce', 'esp_nonce'); ?>
        
        <div class="esp-form-section">
            <h3><?php _e('Provincial Headquarters', 'esp-admin-manager'); ?></h3>
            
            <div class="esp-form-row">
                <label for="address"><?php _e('Physical Address', 'esp-admin-manager'); ?></label>
                <div>
                    <textarea id="address" name="address" rows="4" class="large-text"><?php echo esc_textarea($contact['address'] ?? 'Wewak, East Sepik Province, Papua New Guinea, PO Box 280, Wewak'); ?></textarea>
                    <div class="description"><?php _e('Complete physical address including postal box if applicable', 'esp-admin-manager'); ?></div>
                </div>
            </div>
        </div>

        <div class="esp-form-section">
            <h3><?php _e('Phone & Fax Numbers', 'esp-admin-manager'); ?></h3>
            
            <div class="esp-form-row">
                <label for="phone"><?php _e('Main Phone Number', 'esp-admin-manager'); ?></label>
                <div>
                    <input type="text" id="phone" name="phone" 
                           value="<?php echo esc_attr($contact['phone'] ?? '(+*************'); ?>" 
                           class="regular-text" />
                    <div class="description"><?php _e('Main switchboard or reception number', 'esp-admin-manager'); ?></div>
                </div>
            </div>

            <div class="esp-form-row">
                <label for="fax"><?php _e('Fax Number', 'esp-admin-manager'); ?></label>
                <div>
                    <input type="text" id="fax" name="fax" 
                           value="<?php echo esc_attr($contact['fax'] ?? '(+*************'); ?>" 
                           class="regular-text" />
                    <div class="description"><?php _e('Official fax number for documents', 'esp-admin-manager'); ?></div>
                </div>
            </div>

            <div class="esp-form-row">
                <label for="emergency"><?php _e('Emergency Number', 'esp-admin-manager'); ?></label>
                <div>
                    <input type="text" id="emergency" name="emergency" 
                           value="<?php echo esc_attr($contact['emergency'] ?? '000'); ?>" 
                           class="regular-text" />
                    <div class="description"><?php _e('Emergency services contact number', 'esp-admin-manager'); ?></div>
                </div>
            </div>
        </div>

        <div class="esp-form-section">
            <h3><?php _e('Email & Web', 'esp-admin-manager'); ?></h3>
            
            <div class="esp-form-row">
                <label for="email"><?php _e('General Email', 'esp-admin-manager'); ?></label>
                <div>
                    <input type="email" id="email" name="email" 
                           value="<?php echo esc_attr($contact['email'] ?? '<EMAIL>'); ?>" 
                           class="regular-text" />
                    <div class="description"><?php _e('General inquiries email address', 'esp-admin-manager'); ?></div>
                </div>
            </div>

            <div class="esp-form-row">
                <label for="admin_email"><?php _e('Administration Email', 'esp-admin-manager'); ?></label>
                <div>
                    <input type="email" id="admin_email" name="admin_email" 
                           value="<?php echo esc_attr($contact['admin_email'] ?? '<EMAIL>'); ?>" 
                           class="regular-text" />
                    <div class="description"><?php _e('Administrative matters email address', 'esp-admin-manager'); ?></div>
                </div>
            </div>

            <div class="esp-form-row">
                <label for="website"><?php _e('Official Website', 'esp-admin-manager'); ?></label>
                <div>
                    <input type="text" id="website" name="website" 
                           value="<?php echo esc_attr($contact['website'] ?? 'www.eastsepik.gov.pg'); ?>" 
                           class="regular-text" />
                    <div class="description"><?php _e('Official website URL (without http://)', 'esp-admin-manager'); ?></div>
                </div>
            </div>
        </div>

        <div class="esp-form-section">
            <h3><?php _e('Office Hours', 'esp-admin-manager'); ?></h3>
            
            <div class="esp-form-row">
                <label for="office_hours"><?php _e('Operating Hours', 'esp-admin-manager'); ?></label>
                <div>
                    <textarea id="office_hours" name="office_hours" rows="3" class="large-text"><?php echo esc_textarea($contact['office_hours'] ?? 'Monday - Friday, 8:00 AM - 4:30 PM, Closed Weekends & Public Holidays'); ?></textarea>
                    <div class="description"><?php _e('When the provincial offices are open to the public', 'esp-admin-manager'); ?></div>
                </div>
            </div>
        </div>

        <p class="submit">
            <input type="submit" name="submit" class="button-primary esp-button large" 
                   value="<?php _e('Save Contact Information', 'esp-admin-manager'); ?>" />
        </p>
    </form>

    <!-- Preview Section -->
    <div class="esp-form-section">
        <h3><?php _e('Preview', 'esp-admin-manager'); ?></h3>
        <p><?php _e('This is how the contact information will appear on your website:', 'esp-admin-manager'); ?></p>
        
        <div style="border: 1px solid #ddd; padding: 20px; background: #f9f9f9; border-radius: 8px;">
            <?php echo do_shortcode('[esp_contact]'); ?>
        </div>
        
        <p style="margin-top: 15px;">
            <strong><?php _e('Shortcode:', 'esp-admin-manager'); ?></strong> 
            <code>[esp_contact]</code>
        </p>
        <p>
            <?php _e('You can also use:', 'esp-admin-manager'); ?> <code>[esp_contact layout="list"]</code> 
            <?php _e('for a vertical list layout.', 'esp-admin-manager'); ?>
        </p>
    </div>

    <!-- Quick Contact Cards -->
    <div class="esp-form-section">
        <h3><?php _e('Quick Reference', 'esp-admin-manager'); ?></h3>
        <div class="esp-dashboard-cards">
            <div class="esp-dashboard-card">
                <span class="esp-dashboard-card-icon">📞</span>
                <h3><?php _e('Main Phone', 'esp-admin-manager'); ?></h3>
                <div class="count" style="font-size: 18px;"><?php echo esc_html($contact['phone'] ?? '(+*************'); ?></div>
                <div class="description"><?php _e('Primary contact number', 'esp-admin-manager'); ?></div>
            </div>

            <div class="esp-dashboard-card">
                <span class="esp-dashboard-card-icon">✉️</span>
                <h3><?php _e('General Email', 'esp-admin-manager'); ?></h3>
                <div class="count" style="font-size: 14px;"><?php echo esc_html($contact['email'] ?? '<EMAIL>'); ?></div>
                <div class="description"><?php _e('General inquiries', 'esp-admin-manager'); ?></div>
            </div>

            <div class="esp-dashboard-card">
                <span class="esp-dashboard-card-icon">🚨</span>
                <h3><?php _e('Emergency', 'esp-admin-manager'); ?></h3>
                <div class="count" style="font-size: 24px; color: #dc3232;"><?php echo esc_html($contact['emergency'] ?? '000'); ?></div>
                <div class="description"><?php _e('Emergency services', 'esp-admin-manager'); ?></div>
            </div>

            <div class="esp-dashboard-card">
                <span class="esp-dashboard-card-icon">🌐</span>
                <h3><?php _e('Website', 'esp-admin-manager'); ?></h3>
                <div class="count" style="font-size: 14px;"><?php echo esc_html($contact['website'] ?? 'www.eastsepik.gov.pg'); ?></div>
                <div class="description"><?php _e('Official website', 'esp-admin-manager'); ?></div>
            </div>
        </div>
    </div>

    <!-- Help Section -->
    <div class="esp-help">
        <h4><?php _e('Contact Information Help', 'esp-admin-manager'); ?></h4>
        <p><?php _e('Accurate contact information is essential for citizens to reach government services. Consider these best practices:', 'esp-admin-manager'); ?></p>
        <ul style="margin: 10px 0 0 20px;">
            <li><?php _e('Keep all contact information current and verify regularly', 'esp-admin-manager'); ?></li>
            <li><?php _e('Use international format for phone numbers: (+675) XXX-XXXX', 'esp-admin-manager'); ?></li>
            <li><?php _e('Provide multiple contact methods for accessibility', 'esp-admin-manager'); ?></li>
            <li><?php _e('Include clear office hours to manage citizen expectations', 'esp-admin-manager'); ?></li>
            <li><?php _e('Test email addresses and phone numbers periodically', 'esp-admin-manager'); ?></li>
            <li><?php _e('Consider providing department-specific contact information', 'esp-admin-manager'); ?></li>
        </ul>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Format phone numbers as user types
    $('#phone, #fax').on('input', function() {
        let value = $(this).val().replace(/\D/g, '');
        if (value.startsWith('675')) {
            value = '(+675) ' + value.substring(3);
        }
        // Add more formatting logic as needed
    });
    
    // Validate email addresses
    $('input[type="email"]').on('blur', function() {
        let email = $(this).val();
        let emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        
        if (email && !emailRegex.test(email)) {
            $(this).css('border-color', '#dc3232');
            if (!$(this).next('.error-message').length) {
                $(this).after('<div class="error-message" style="color: #dc3232; font-size: 12px; margin-top: 5px;">Please enter a valid email address</div>');
            }
        } else {
            $(this).css('border-color', '');
            $(this).next('.error-message').remove();
        }
    });
    
    // Website URL formatting
    $('#website').on('blur', function() {
        let url = $(this).val();
        if (url && !url.startsWith('www.') && !url.startsWith('http')) {
            $(this).val('www.' + url);
        }
    });
});
</script>
