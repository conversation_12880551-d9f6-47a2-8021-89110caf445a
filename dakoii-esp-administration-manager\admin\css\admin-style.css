/* ESP Administration Manager - Admin Styles */

:root {
    --esp-primary: #006A4E;
    --esp-secondary: #CE1126;
    --esp-accent: #FFD700;
    --esp-light: #f8fafc;
    --esp-dark: #004d3a;
}

/* Admin Page Header */
.esp-admin-header {
    background: linear-gradient(135deg, var(--esp-primary), var(--esp-dark));
    color: white;
    padding: 20px;
    margin: 0 -20px 20px -20px;
    border-radius: 8px;
}

.esp-admin-header h1 {
    margin: 0;
    font-size: 28px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.esp-admin-header h1::before {
    content: '🏛️';
    font-size: 32px;
}

.esp-admin-header p {
    margin: 10px 0 0 0;
    opacity: 0.9;
    font-size: 16px;
}

/* Dashboard Cards */
.esp-dashboard-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.esp-dashboard-card {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.esp-dashboard-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.esp-dashboard-card-icon {
    font-size: 48px;
    margin-bottom: 15px;
    display: block;
}

.esp-dashboard-card h3 {
    margin: 0 0 10px 0;
    color: var(--esp-primary);
    font-size: 18px;
}

.esp-dashboard-card .count {
    font-size: 32px;
    font-weight: bold;
    color: var(--esp-secondary);
    margin-bottom: 10px;
}

.esp-dashboard-card .description {
    color: #666;
    font-size: 14px;
    margin-bottom: 15px;
}

.esp-dashboard-card .button {
    background: var(--esp-primary);
    color: white;
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 4px;
    display: inline-block;
    transition: background 0.3s ease;
}

.esp-dashboard-card .button:hover {
    background: var(--esp-dark);
    color: white;
}

/* Form Styles */
.esp-form-section {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.esp-form-section h3 {
    margin: 0 0 20px 0;
    color: var(--esp-primary);
    border-bottom: 2px solid var(--esp-accent);
    padding-bottom: 10px;
}

.esp-form-row {
    display: grid;
    grid-template-columns: 200px 1fr;
    gap: 15px;
    margin-bottom: 20px;
    align-items: start;
}

.esp-form-row label {
    font-weight: 600;
    color: #333;
    padding-top: 5px;
}

.esp-form-row input,
.esp-form-row textarea,
.esp-form-row select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.esp-form-row input:focus,
.esp-form-row textarea:focus,
.esp-form-row select:focus {
    border-color: var(--esp-primary);
    outline: none;
    box-shadow: 0 0 0 2px rgba(0, 106, 78, 0.1);
}

.esp-form-row .description {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
    font-style: italic;
}

/* Statistics Grid */
.esp-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.esp-stats-item {
    background: var(--esp-light);
    padding: 15px;
    border-radius: 6px;
    border-left: 4px solid var(--esp-primary);
}

.esp-stats-item label {
    display: block;
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--esp-primary);
}

.esp-stats-item input {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-align: center;
    font-size: 16px;
    font-weight: bold;
}

/* List Tables */
.esp-list-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.esp-list-table th {
    background: var(--esp-primary);
    color: white;
    padding: 15px;
    text-align: left;
    font-weight: 600;
}

.esp-list-table td {
    padding: 15px;
    border-bottom: 1px solid #eee;
}

.esp-list-table tr:hover {
    background: var(--esp-light);
}

.esp-list-table .actions {
    white-space: nowrap;
}

.esp-list-table .actions a {
    margin-right: 10px;
    text-decoration: none;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 12px;
}

.esp-list-table .actions .edit {
    background: #0073aa;
    color: white;
}

.esp-list-table .actions .delete {
    background: #dc3232;
    color: white;
}

/* Buttons */
.esp-button {
    background: var(--esp-primary);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
}

.esp-button:hover {
    background: var(--esp-dark);
    color: white;
}

.esp-button.secondary {
    background: var(--esp-secondary);
}

.esp-button.secondary:hover {
    background: #a00e1f;
}

.esp-button.large {
    padding: 15px 30px;
    font-size: 16px;
}

/* Messages */
.esp-message {
    padding: 12px 15px;
    border-radius: 4px;
    margin-bottom: 20px;
}

.esp-message.success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.esp-message.error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.esp-message.warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

/* Tabs */
.esp-tabs {
    margin-bottom: 20px;
}

.esp-tab-nav {
    display: flex;
    border-bottom: 2px solid #ddd;
    margin-bottom: 20px;
}

.esp-tab-nav button {
    background: none;
    border: none;
    padding: 12px 20px;
    cursor: pointer;
    font-size: 14px;
    color: #666;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.esp-tab-nav button.active {
    color: var(--esp-primary);
    border-bottom-color: var(--esp-primary);
}

.esp-tab-nav button:hover {
    color: var(--esp-primary);
}

.esp-tab-content {
    display: none;
}

.esp-tab-content.active {
    display: block;
}

/* Media Upload */
.esp-media-upload {
    border: 2px dashed #ddd;
    padding: 40px;
    text-align: center;
    border-radius: 8px;
    background: #fafafa;
    transition: all 0.3s ease;
}

.esp-media-upload:hover {
    border-color: var(--esp-primary);
    background: var(--esp-light);
}

.esp-media-upload.has-image {
    border-style: solid;
    border-color: var(--esp-primary);
}

.esp-media-preview {
    max-width: 200px;
    max-height: 200px;
    margin: 0 auto 15px;
    border-radius: 8px;
    overflow: hidden;
}

.esp-media-preview img {
    width: 100%;
    height: auto;
    display: block;
}

/* Responsive */
@media (max-width: 768px) {
    .esp-dashboard-cards {
        grid-template-columns: 1fr;
    }
    
    .esp-form-row {
        grid-template-columns: 1fr;
        gap: 5px;
    }
    
    .esp-stats-grid {
        grid-template-columns: 1fr;
    }
    
    .esp-tab-nav {
        flex-wrap: wrap;
    }
    
    .esp-list-table {
        font-size: 12px;
    }
    
    .esp-list-table th,
    .esp-list-table td {
        padding: 8px;
    }
}

/* Loading States */
.esp-loading {
    opacity: 0.6;
    pointer-events: none;
}

.esp-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--esp-primary);
    border-radius: 50%;
    animation: esp-spin 1s linear infinite;
}

@keyframes esp-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Help Text */
.esp-help {
    background: #e7f3ff;
    border: 1px solid #b8daff;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
}

.esp-help h4 {
    margin: 0 0 10px 0;
    color: #004085;
}

.esp-help p {
    margin: 0;
    color: #004085;
    font-size: 14px;
}

/* Quick Actions */
.esp-quick-actions {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.esp-quick-actions h3 {
    margin: 0 0 15px 0;
    color: var(--esp-primary);
}

.esp-quick-actions .actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.esp-quick-actions .actions a {
    background: var(--esp-light);
    color: var(--esp-primary);
    text-decoration: none;
    padding: 8px 15px;
    border-radius: 4px;
    border: 1px solid #ddd;
    transition: all 0.3s ease;
}

.esp-quick-actions .actions a:hover {
    background: var(--esp-primary);
    color: white;
}
