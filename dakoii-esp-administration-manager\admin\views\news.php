<?php
/**
 * ESP Administration Manager - News Management View
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get all news
$news = get_posts(array(
    'post_type' => 'esp_news',
    'numberposts' => -1,
    'post_status' => 'any',
    'orderby' => 'date',
    'order' => 'DESC'
));
?>

<div class="wrap">
    <div class="esp-admin-header">
        <h1><?php _e('News Management', 'esp-admin-manager'); ?></h1>
        <p><?php _e('Manage government news, announcements, and public information', 'esp-admin-manager'); ?></p>
    </div>

    <?php settings_errors('esp_messages'); ?>

    <!-- Quick Actions -->
    <div class="esp-quick-actions">
        <h3><?php _e('Quick Actions', 'esp-admin-manager'); ?></h3>
        <div class="actions">
            <a href="<?php echo admin_url('post-new.php?post_type=esp_news'); ?>" class="esp-button">
                <?php _e('Add News Article', 'esp-admin-manager'); ?>
            </a>
            <a href="<?php echo admin_url('edit.php?post_type=esp_news'); ?>" class="esp-button secondary">
                <?php _e('Manage All News', 'esp-admin-manager'); ?>
            </a>
        </div>
    </div>

    <!-- News List -->
    <div class="esp-form-section">
        <h3><?php _e('Recent News Articles', 'esp-admin-manager'); ?></h3>
        
        <?php if (!empty($news)): ?>
        <table class="esp-list-table">
            <thead>
                <tr>
                    <th><?php _e('Title', 'esp-admin-manager'); ?></th>
                    <th><?php _e('Date', 'esp-admin-manager'); ?></th>
                    <th><?php _e('Source', 'esp-admin-manager'); ?></th>
                    <th><?php _e('Featured', 'esp-admin-manager'); ?></th>
                    <th><?php _e('Status', 'esp-admin-manager'); ?></th>
                    <th><?php _e('Actions', 'esp-admin-manager'); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($news as $news_item): 
                    $news_date = get_post_meta($news_item->ID, '_esp_news_date', true);
                    $source = get_post_meta($news_item->ID, '_esp_news_source', true);
                    $featured = get_post_meta($news_item->ID, '_esp_news_featured', true);
                ?>
                <tr>
                    <td>
                        <strong><?php echo esc_html($news_item->post_title); ?></strong>
                        <div style="font-size: 12px; color: #666;">
                            <?php 
                            if ($news_item->post_excerpt) {
                                echo esc_html(wp_trim_words($news_item->post_excerpt, 15));
                            } else {
                                echo esc_html(wp_trim_words($news_item->post_content, 15));
                            }
                            ?>
                        </div>
                    </td>
                    <td>
                        <?php if ($news_date): ?>
                            <div style="font-weight: bold;">
                                <?php echo esc_html(date('M j, Y', strtotime($news_date))); ?>
                            </div>
                        <?php else: ?>
                            <div style="font-weight: bold;">
                                <?php echo esc_html(get_the_date('M j, Y', $news_item)); ?>
                            </div>
                        <?php endif; ?>
                        <div style="font-size: 12px; color: #666;">
                            <?php echo esc_html(get_the_date('g:i A', $news_item)); ?>
                        </div>
                    </td>
                    <td>
                        <?php if ($source): ?>
                            <span style="background: var(--esp-light); padding: 2px 8px; border-radius: 12px; font-size: 12px;">
                                <?php echo esc_html($source); ?>
                            </span>
                        <?php else: ?>
                            <span style="color: #999;">—</span>
                        <?php endif; ?>
                    </td>
                    <td>
                        <?php if ($featured === '1'): ?>
                            <span style="color: gold; font-size: 16px;">★</span> <?php _e('Featured', 'esp-admin-manager'); ?>
                        <?php else: ?>
                            <span style="color: #ccc; font-size: 16px;">☆</span> <?php _e('Regular', 'esp-admin-manager'); ?>
                        <?php endif; ?>
                    </td>
                    <td>
                        <?php if ($news_item->post_status === 'publish'): ?>
                            <span style="color: green;">●</span> <?php _e('Published', 'esp-admin-manager'); ?>
                        <?php else: ?>
                            <span style="color: orange;">●</span> <?php echo esc_html(ucfirst($news_item->post_status)); ?>
                        <?php endif; ?>
                    </td>
                    <td class="actions">
                        <a href="<?php echo get_edit_post_link($news_item->ID); ?>" class="edit">
                            <?php _e('Edit', 'esp-admin-manager'); ?>
                        </a>
                        <a href="<?php echo get_delete_post_link($news_item->ID); ?>" class="delete" 
                           onclick="return confirm('<?php _e('Are you sure you want to delete this news article?', 'esp-admin-manager'); ?>')">
                            <?php _e('Delete', 'esp-admin-manager'); ?>
                        </a>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <?php else: ?>
        <div class="esp-message warning">
            <p><?php _e('No news articles found. Click "Add News Article" to get started.', 'esp-admin-manager'); ?></p>
        </div>
        <?php endif; ?>
    </div>

    <!-- News Statistics -->
    <div class="esp-form-section">
        <h3><?php _e('News Statistics', 'esp-admin-manager'); ?></h3>
        <div class="esp-stats-grid">
            <div class="esp-stats-item">
                <label><?php _e('Total Articles', 'esp-admin-manager'); ?></label>
                <div style="font-size: 24px; font-weight: bold; color: var(--esp-secondary);">
                    <?php echo count($news); ?>
                </div>
            </div>
            
            <div class="esp-stats-item">
                <label><?php _e('Featured Articles', 'esp-admin-manager'); ?></label>
                <div style="font-size: 24px; font-weight: bold; color: gold;">
                    <?php 
                    $featured_count = 0;
                    foreach ($news as $news_item) {
                        $featured = get_post_meta($news_item->ID, '_esp_news_featured', true);
                        if ($featured === '1') {
                            $featured_count++;
                        }
                    }
                    echo $featured_count;
                    ?>
                </div>
            </div>
            
            <div class="esp-stats-item">
                <label><?php _e('Published', 'esp-admin-manager'); ?></label>
                <div style="font-size: 24px; font-weight: bold; color: green;">
                    <?php echo count(array_filter($news, function($news_item) { return $news_item->post_status === 'publish'; })); ?>
                </div>
            </div>
            
            <div class="esp-stats-item">
                <label><?php _e('This Month', 'esp-admin-manager'); ?></label>
                <div style="font-size: 24px; font-weight: bold; color: var(--esp-primary);">
                    <?php 
                    $this_month = 0;
                    $current_month = date('Y-m');
                    foreach ($news as $news_item) {
                        $news_date = get_post_meta($news_item->ID, '_esp_news_date', true);
                        $check_date = $news_date ?: get_the_date('Y-m-d', $news_item);
                        if ($check_date && substr($check_date, 0, 7) === $current_month) {
                            $this_month++;
                        }
                    }
                    echo $this_month;
                    ?>
                </div>
            </div>
        </div>
    </div>

    <!-- News Sources Breakdown -->
    <?php if (!empty($news)): ?>
    <div class="esp-form-section">
        <h3><?php _e('News by Source', 'esp-admin-manager'); ?></h3>
        <?php
        $sources = array();
        foreach ($news as $news_item) {
            $source = get_post_meta($news_item->ID, '_esp_news_source', true);
            if ($source) {
                $sources[$source] = isset($sources[$source]) ? $sources[$source] + 1 : 1;
            }
        }
        arsort($sources);
        ?>
        
        <?php if (!empty($sources)): ?>
        <div class="esp-dashboard-cards">
            <?php foreach ($sources as $source => $count): ?>
            <div class="esp-dashboard-card">
                <h3><?php echo esc_html($source); ?></h3>
                <div class="count"><?php echo esc_html($count); ?></div>
                <div class="description"><?php echo sprintf(_n('%d article', '%d articles', $count, 'esp-admin-manager'), $count); ?></div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
    </div>
    <?php endif; ?>

    <!-- Preview Section -->
    <div class="esp-form-section">
        <h3><?php _e('Preview', 'esp-admin-manager'); ?></h3>
        <p><?php _e('This is how the news will appear on your website:', 'esp-admin-manager'); ?></p>
        
        <div style="border: 1px solid #ddd; padding: 20px; background: #f9f9f9; border-radius: 8px; max-height: 400px; overflow-y: auto;">
            <?php echo do_shortcode('[esp_news limit="5"]'); ?>
        </div>
        
        <p style="margin-top: 15px;">
            <strong><?php _e('Shortcode:', 'esp-admin-manager'); ?></strong> 
            <code>[esp_news]</code>
        </p>
        <p>
            <?php _e('Options:', 'esp-admin-manager'); ?> 
            <code>[esp_news limit="5"]</code> - <?php _e('Show only 5 articles', 'esp-admin-manager'); ?><br>
            <code>[esp_news featured_only="true"]</code> - <?php _e('Show only featured articles', 'esp-admin-manager'); ?>
        </p>
    </div>

    <!-- Help Section -->
    <div class="esp-help">
        <h4><?php _e('Managing News', 'esp-admin-manager'); ?></h4>
        <p><?php _e('News articles keep citizens informed about government activities and important announcements. Here are some guidelines:', 'esp-admin-manager'); ?></p>
        <ul style="margin: 10px 0 0 20px;">
            <li><?php _e('Write clear, informative headlines that summarize the news', 'esp-admin-manager'); ?></li>
            <li><?php _e('Include the source department or office for credibility', 'esp-admin-manager'); ?></li>
            <li><?php _e('Use the excerpt field for a brief summary that appears in listings', 'esp-admin-manager'); ?></li>
            <li><?php _e('Mark important announcements as "Featured" for prominence', 'esp-admin-manager'); ?></li>
            <li><?php _e('Set appropriate news dates, especially for scheduled announcements', 'esp-admin-manager'); ?></li>
            <li><?php _e('Keep articles factual, clear, and accessible to all citizens', 'esp-admin-manager'); ?></li>
        </ul>
    </div>
</div>
