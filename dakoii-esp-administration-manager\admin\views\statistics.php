<?php
/**
 * ESP Administration Manager - Provincial Statistics View
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get current statistics
$statistics = get_option('esp_provincial_statistics', array());
?>

<div class="wrap">
    <div class="esp-admin-header">
        <h1><?php _e('Provincial Statistics', 'esp-admin-manager'); ?></h1>
        <p><?php _e('Manage key statistics and demographic data for East Sepik Province', 'esp-admin-manager'); ?></p>
    </div>

    <?php settings_errors('esp_messages'); ?>

    <form method="post" action="">
        <?php wp_nonce_field('esp_statistics_nonce', 'esp_nonce'); ?>
        
        <div class="esp-form-section">
            <h3><?php _e('Provincial Overview Statistics', 'esp-admin-manager'); ?></h3>
            
            <div class="esp-stats-grid">
                <div class="esp-stats-item">
                    <label for="population"><?php _e('Population', 'esp-admin-manager'); ?></label>
                    <input type="text" id="population" name="population" 
                           value="<?php echo esc_attr($statistics['population'] ?? '450,530'); ?>" 
                           placeholder="450,530" />
                </div>

                <div class="esp-stats-item">
                    <label for="area"><?php _e('Area (km²)', 'esp-admin-manager'); ?></label>
                    <input type="text" id="area" name="area" 
                           value="<?php echo esc_attr($statistics['area'] ?? '43,426'); ?>" 
                           placeholder="43,426" />
                </div>

                <div class="esp-stats-item">
                    <label for="districts"><?php _e('Districts', 'esp-admin-manager'); ?></label>
                    <input type="number" id="districts" name="districts" 
                           value="<?php echo esc_attr($statistics['districts'] ?? '6'); ?>" 
                           placeholder="6" min="1" />
                </div>

                <div class="esp-stats-item">
                    <label for="llgs"><?php _e('Local Level Governments (LLGs)', 'esp-admin-manager'); ?></label>
                    <input type="number" id="llgs" name="llgs" 
                           value="<?php echo esc_attr($statistics['llgs'] ?? '41'); ?>" 
                           placeholder="41" min="1" />
                </div>

                <div class="esp-stats-item">
                    <label for="wards"><?php _e('Wards', 'esp-admin-manager'); ?></label>
                    <input type="text" id="wards" name="wards" 
                           value="<?php echo esc_attr($statistics['wards'] ?? '1,287'); ?>" 
                           placeholder="1,287" />
                </div>

                <div class="esp-stats-item">
                    <label for="urban_llgs"><?php _e('Urban LLGs', 'esp-admin-manager'); ?></label>
                    <input type="number" id="urban_llgs" name="urban_llgs" 
                           value="<?php echo esc_attr($statistics['urban_llgs'] ?? '4'); ?>" 
                           placeholder="4" min="0" />
                </div>
            </div>
        </div>

        <p class="submit">
            <input type="submit" name="submit" class="button-primary esp-button large" 
                   value="<?php _e('Save Statistics', 'esp-admin-manager'); ?>" />
        </p>
    </form>

    <!-- Preview Section -->
    <div class="esp-form-section">
        <h3><?php _e('Preview', 'esp-admin-manager'); ?></h3>
        <p><?php _e('This is how the provincial statistics will appear on your website:', 'esp-admin-manager'); ?></p>
        
        <div style="border: 1px solid #ddd; padding: 20px; background: #f9f9f9; border-radius: 8px;">
            <?php echo do_shortcode('[esp_statistics]'); ?>
        </div>
        
        <p style="margin-top: 15px;">
            <strong><?php _e('Shortcode:', 'esp-admin-manager'); ?></strong> 
            <code>[esp_statistics]</code>
        </p>
        <p>
            <?php _e('You can also use:', 'esp-admin-manager'); ?> <code>[esp_statistics layout="list"]</code> 
            <?php _e('for a vertical list layout.', 'esp-admin-manager'); ?>
        </p>
    </div>

    <!-- Help Section -->
    <div class="esp-help">
        <h4><?php _e('Statistics Help', 'esp-admin-manager'); ?></h4>
        <p><?php _e('Provincial statistics provide important demographic and administrative information. Here are some guidelines:', 'esp-admin-manager'); ?></p>
        <ul style="margin: 10px 0 0 20px;">
            <li><?php _e('Use official census data for population figures when available', 'esp-admin-manager'); ?></li>
            <li><?php _e('Format large numbers with commas for better readability (e.g., 450,530)', 'esp-admin-manager'); ?></li>
            <li><?php _e('Update statistics regularly when new official data becomes available', 'esp-admin-manager'); ?></li>
            <li><?php _e('Ensure all numbers are accurate as they represent official government data', 'esp-admin-manager'); ?></li>
            <li><?php _e('The statistics can be displayed using the [esp_statistics] shortcode', 'esp-admin-manager'); ?></li>
        </ul>
    </div>

    <!-- Data Sources Section -->
    <div class="esp-form-section">
        <h3><?php _e('Data Sources & Notes', 'esp-admin-manager'); ?></h3>
        <div class="esp-form-row">
            <label><?php _e('Recommended Sources', 'esp-admin-manager'); ?></label>
            <div>
                <ul style="margin: 0;">
                    <li><?php _e('Papua New Guinea National Statistical Office', 'esp-admin-manager'); ?></li>
                    <li><?php _e('Department of Provincial and Local Government Affairs', 'esp-admin-manager'); ?></li>
                    <li><?php _e('East Sepik Provincial Administration Records', 'esp-admin-manager'); ?></li>
                    <li><?php _e('PNG Electoral Commission (for electoral boundaries)', 'esp-admin-manager'); ?></li>
                </ul>
            </div>
        </div>
        
        <div class="esp-form-row">
            <label><?php _e('Last Updated', 'esp-admin-manager'); ?></label>
            <div>
                <p><?php echo esc_html(date('F j, Y')); ?></p>
                <div class="description"><?php _e('Statistics were last updated today. Consider adding a note about data sources and update frequency.', 'esp-admin-manager'); ?></div>
            </div>
        </div>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Format number inputs with commas
    $('#population, #wards').on('input', function() {
        let value = $(this).val().replace(/,/g, '');
        if (!isNaN(value) && value !== '') {
            $(this).val(parseInt(value).toLocaleString());
        }
    });
    
    // Validate numeric inputs
    $('input[type="number"]').on('input', function() {
        let value = parseInt($(this).val());
        let min = parseInt($(this).attr('min')) || 0;
        
        if (value < min) {
            $(this).val(min);
        }
    });
    
    // Auto-calculate totals if needed
    $('#districts, #llgs').on('input', function() {
        // Could add validation logic here
        // e.g., ensure LLGs >= Districts
    });
});
</script>
