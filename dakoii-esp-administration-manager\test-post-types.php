<?php
/**
 * Test Post Types Registration
 * 
 * Simple test to check if post types are registered
 * Access via: /wp-content/plugins/dakoii-esp-administration-manager/test-post-types.php
 */

// Load WordPress
require_once('../../../wp-load.php');

// Check if user is admin
if (!current_user_can('manage_options')) {
    wp_die('Access denied');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>ESP Post Types Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>ESP Post Types Registration Test</h1>
    
    <h2>All Registered Post Types</h2>
    <table>
        <tr>
            <th>Post Type</th>
            <th>Status</th>
            <th>Show UI</th>
            <th>Show in Menu</th>
        </tr>
        <?php
        $all_post_types = get_post_types(array(), 'objects');
        foreach ($all_post_types as $post_type) {
            $is_esp = strpos($post_type->name, 'esp_') === 0;
            $class = $is_esp ? 'success' : '';
            echo "<tr class='$class'>";
            echo "<td><strong>" . esc_html($post_type->name) . "</strong></td>";
            echo "<td>" . ($is_esp ? '✓ ESP Type' : 'WordPress Default') . "</td>";
            echo "<td>" . ($post_type->show_ui ? '✓' : '✗') . "</td>";
            echo "<td>" . ($post_type->show_in_menu ? esc_html($post_type->show_in_menu) : '✗') . "</td>";
            echo "</tr>";
        }
        ?>
    </table>
    
    <h2>ESP Post Types Check</h2>
    <?php
    $esp_types = array('esp_governor', 'esp_mp', 'esp_district', 'esp_event', 'esp_news');
    foreach ($esp_types as $type) {
        $exists = post_type_exists($type);
        $class = $exists ? 'success' : 'error';
        echo "<p class='$class'>$type: " . ($exists ? '✓ Registered' : '✗ NOT Registered') . "</p>";
    }
    ?>
    
    <h2>Test URLs</h2>
    <p><a href="<?php echo admin_url('post-new.php?post_type=esp_mp'); ?>" target="_blank">Add New MP</a></p>
    <p><a href="<?php echo admin_url('edit.php?post_type=esp_mp'); ?>" target="_blank">Manage All MPs</a></p>
    
    <h2>Plugin Status</h2>
    <?php
    $plugin_file = 'dakoii-esp-administration-manager/dakoii-esp-admin-manager.php';
    $is_active = is_plugin_active($plugin_file);
    $class = $is_active ? 'success' : 'error';
    echo "<p class='$class'>Plugin Active: " . ($is_active ? '✓ Yes' : '✗ No') . "</p>";
    
    // Check if classes exist
    $classes = array('ESP_Administration_Manager', 'ESP_Post_Types', 'ESP_Admin');
    foreach ($classes as $class_name) {
        $exists = class_exists($class_name);
        $class = $exists ? 'success' : 'error';
        echo "<p class='$class'>Class $class_name: " . ($exists ? '✓ Loaded' : '✗ Not Loaded') . "</p>";
    }
    ?>
    
    <h2>Force Registration Test</h2>
    <?php
    if (isset($_GET['force_register'])) {
        echo "<p class='info'>Attempting to force register post types...</p>";
        
        if (class_exists('ESP_Post_Types')) {
            $post_types_instance = ESP_Post_Types::get_instance();
            $post_types_instance->register_post_types();
            echo "<p class='success'>Force registration completed. Check results above.</p>";
        } else {
            echo "<p class='error'>ESP_Post_Types class not found!</p>";
        }
    } else {
        echo "<p><a href='?force_register=1'>Force Register Post Types</a></p>";
    }
    ?>
    
    <h2>WordPress Info</h2>
    <p><strong>WordPress Version:</strong> <?php echo get_bloginfo('version'); ?></p>
    <p><strong>Current User:</strong> <?php echo wp_get_current_user()->user_login; ?></p>
    <p><strong>Current Hook:</strong> <?php echo current_action(); ?></p>
    
</body>
</html>
