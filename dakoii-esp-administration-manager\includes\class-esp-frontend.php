<?php
/**
 * ESP Frontend Class
 * 
 * Handles frontend functionality and template functions
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class ESP_Frontend {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        add_action('wp_head', array($this, 'add_custom_css_variables'));
    }
    
    /**
     * Add custom CSS variables to head
     */
    public function add_custom_css_variables() {
        ?>
        <style>
        :root {
            --png-red: #CE1126;
            --png-green: #006A4E;
            --png-yellow: #FFD700;
            --dark-green: #004d3a;
            --light-green: #00a86b;
            --cream: #FFF8DC;
            --dark-brown: #8B4513;
            --official-blue: #1e3a8a;
            --light-gray: #f8fafc;
            --medium-gray: #64748b;
        }
        </style>
        <?php
    }
    
    /**
     * Get governor data
     */
    public static function get_governor() {
        $governors = get_posts(array(
            'post_type' => 'esp_governor',
            'numberposts' => 1,
            'post_status' => 'publish'
        ));
        
        if (empty($governors)) {
            return null;
        }
        
        $governor = $governors[0];
        
        return array(
            'id' => $governor->ID,
            'name' => $governor->post_title,
            'message' => $governor->post_content,
            'title' => get_post_meta($governor->ID, '_esp_governor_title', true),
            'party' => get_post_meta($governor->ID, '_esp_governor_party', true),
            'email' => get_post_meta($governor->ID, '_esp_governor_email', true),
            'phone' => get_post_meta($governor->ID, '_esp_governor_phone', true),
            'photo' => get_the_post_thumbnail_url($governor->ID, 'medium')
        );
    }
    
    /**
     * Get MPs data
     */
    public static function get_mps($limit = -1) {
        $mps = get_posts(array(
            'post_type' => 'esp_mp',
            'numberposts' => $limit,
            'post_status' => 'publish',
            'orderby' => 'title',
            'order' => 'ASC'
        ));
        
        $mps_data = array();
        
        foreach ($mps as $mp) {
            $mps_data[] = array(
                'id' => $mp->ID,
                'name' => $mp->post_title,
                'bio' => $mp->post_content,
                'electorate' => get_post_meta($mp->ID, '_esp_mp_electorate', true),
                'party' => get_post_meta($mp->ID, '_esp_mp_party', true),
                'email' => get_post_meta($mp->ID, '_esp_mp_email', true),
                'phone' => get_post_meta($mp->ID, '_esp_mp_phone', true),
                'photo' => get_the_post_thumbnail_url($mp->ID, 'thumbnail')
            );
        }
        
        return $mps_data;
    }
    
    /**
     * Get districts data
     */
    public static function get_districts($limit = -1) {
        $districts = get_posts(array(
            'post_type' => 'esp_district',
            'numberposts' => $limit,
            'post_status' => 'publish',
            'orderby' => 'title',
            'order' => 'ASC'
        ));
        
        $districts_data = array();
        
        foreach ($districts as $district) {
            $districts_data[] = array(
                'id' => $district->ID,
                'name' => $district->post_title,
                'description' => $district->post_content,
                'llgs' => get_post_meta($district->ID, '_esp_district_llgs', true),
                'wards' => get_post_meta($district->ID, '_esp_district_wards', true),
                'population' => get_post_meta($district->ID, '_esp_district_population', true),
                'area' => get_post_meta($district->ID, '_esp_district_area', true),
                'photo' => get_the_post_thumbnail_url($district->ID, 'medium')
            );
        }
        
        return $districts_data;
    }
    
    /**
     * Get provincial statistics
     */
    public static function get_provincial_statistics() {
        return get_option('esp_provincial_statistics', array());
    }
    
    /**
     * Get administrative structure
     */
    public static function get_administrative_structure() {
        return get_option('esp_administrative_structure', array());
    }
    
    /**
     * Get contact information
     */
    public static function get_contact_information() {
        return get_option('esp_contact_information', array());
    }
    
    /**
     * Get events
     */
    public static function get_events($limit = 5, $upcoming_only = true) {
        $args = array(
            'post_type' => 'esp_event',
            'numberposts' => $limit,
            'post_status' => 'publish',
            'orderby' => 'meta_value',
            'meta_key' => '_esp_event_start_date',
            'order' => 'ASC'
        );
        
        if ($upcoming_only) {
            $args['meta_query'] = array(
                array(
                    'key' => '_esp_event_start_date',
                    'value' => date('Y-m-d'),
                    'compare' => '>='
                )
            );
        }
        
        $events = get_posts($args);
        $events_data = array();
        
        foreach ($events as $event) {
            $events_data[] = array(
                'id' => $event->ID,
                'title' => $event->post_title,
                'description' => $event->post_content,
                'start_date' => get_post_meta($event->ID, '_esp_event_start_date', true),
                'end_date' => get_post_meta($event->ID, '_esp_event_end_date', true),
                'location' => get_post_meta($event->ID, '_esp_event_location', true),
                'contact' => get_post_meta($event->ID, '_esp_event_contact', true),
                'photo' => get_the_post_thumbnail_url($event->ID, 'medium')
            );
        }
        
        return $events_data;
    }
    
    /**
     * Get news
     */
    public static function get_news($limit = 5, $featured_only = false) {
        $args = array(
            'post_type' => 'esp_news',
            'numberposts' => $limit,
            'post_status' => 'publish',
            'orderby' => 'date',
            'order' => 'DESC'
        );
        
        if ($featured_only) {
            $args['meta_query'] = array(
                array(
                    'key' => '_esp_news_featured',
                    'value' => '1',
                    'compare' => '='
                )
            );
        }
        
        $news = get_posts($args);
        $news_data = array();
        
        foreach ($news as $news_item) {
            $news_data[] = array(
                'id' => $news_item->ID,
                'title' => $news_item->post_title,
                'content' => $news_item->post_content,
                'excerpt' => $news_item->post_excerpt,
                'date' => get_post_meta($news_item->ID, '_esp_news_date', true) ?: get_the_date('Y-m-d', $news_item),
                'source' => get_post_meta($news_item->ID, '_esp_news_source', true),
                'featured' => get_post_meta($news_item->ID, '_esp_news_featured', true),
                'photo' => get_the_post_thumbnail_url($news_item->ID, 'medium')
            );
        }
        
        return $news_data;
    }
    
    /**
     * Format date for display
     */
    public static function format_date($date, $format = 'F j, Y') {
        if (empty($date)) {
            return '';
        }
        
        return date($format, strtotime($date));
    }
    
    /**
     * Format date range for events
     */
    public static function format_date_range($start_date, $end_date = '') {
        if (empty($start_date)) {
            return '';
        }
        
        $formatted_start = self::format_date($start_date);
        
        if (!empty($end_date) && $end_date !== $start_date) {
            $formatted_end = self::format_date($end_date);
            return $formatted_start . ' - ' . $formatted_end;
        }
        
        return $formatted_start;
    }
    
    /**
     * Get placeholder image URL
     */
    public static function get_placeholder_image($type = 'person') {
        $placeholders = array(
            'person' => '👤',
            'building' => '🏛️',
            'map' => '🗺️',
            'event' => '🎉',
            'news' => '📰'
        );
        
        return isset($placeholders[$type]) ? $placeholders[$type] : '📷';
    }
    
    /**
     * Truncate text
     */
    public static function truncate_text($text, $length = 150, $suffix = '...') {
        if (strlen($text) <= $length) {
            return $text;
        }
        
        return substr($text, 0, $length) . $suffix;
    }
    
    /**
     * Check if plugin has data
     */
    public static function has_data() {
        $governor = self::get_governor();
        $mps = self::get_mps(1);
        $districts = self::get_districts(1);
        
        return !empty($governor) || !empty($mps) || !empty($districts);
    }
    
    /**
     * Get admin structure as formatted HTML
     */
    public static function get_admin_structure_html() {
        $structure = self::get_administrative_structure();
        
        if (empty($structure)) {
            return '';
        }
        
        $html = '<div class="esp-admin-structure">';
        
        if (isset($structure['government_sectors'])) {
            $html .= '<div class="esp-structure-section">';
            $html .= '<h3>' . __('Government Sectors', 'esp-admin-manager') . '</h3>';
            $html .= wp_kses_post($structure['government_sectors']);
            $html .= '</div>';
        }
        
        if (isset($structure['administrative_divisions'])) {
            $html .= '<div class="esp-structure-section">';
            $html .= '<h3>' . __('Administrative Divisions', 'esp-admin-manager') . '</h3>';
            $html .= wp_kses_post($structure['administrative_divisions']);
            $html .= '</div>';
        }
        
        $html .= '</div>';
        
        return $html;
    }
}
