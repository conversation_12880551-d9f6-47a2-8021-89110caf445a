/* ESP Administration Manager - Public Styles */

:root {
    --png-red: #CE1126;
    --png-green: #006A4E;
    --png-yellow: #FFD700;
    --dark-green: #004d3a;
    --light-green: #00a86b;
    --cream: #FFF8DC;
    --dark-brown: #8B4513;
    --official-blue: #1e3a8a;
    --light-gray: #f8fafc;
    --medium-gray: #64748b;
}

/* Governor Section */
.esp-governor-section {
    padding: 2rem 0;
    background: white;
}

.esp-governor-grid {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 3rem;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.esp-governor-photo {
    width: 100%;
    max-width: 300px;
    min-height: 200px;
    background: linear-gradient(45deg, var(--png-green), var(--png-red));
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 3rem;
    box-shadow: 0 10px 25px rgba(0,0,0,0.2);
    position: relative;
    overflow: hidden;
}

.esp-governor-photo img {
    width: 100%;
    height: auto;
    max-height: 400px;
    object-fit: cover;
    border-radius: 15px;
    display: block;
}

.esp-placeholder-photo {
    font-size: 4rem;
    color: white;
}

.esp-governor-content h2 {
    font-size: 2.5rem;
    color: var(--png-green);
    margin-bottom: 0.5rem;
}

.esp-governor-title {
    font-size: 1.2rem;
    color: var(--png-red);
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.esp-governor-party {
    font-size: 1rem;
    color: var(--medium-gray);
    margin-bottom: 1.5rem;
}

.esp-governor-message {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #555;
    font-style: italic;
    border-left: 4px solid var(--png-yellow);
    padding-left: 1.5rem;
}

/* Parliament Members Section */
.esp-parliament-section {
    padding: 2rem 0;
    background: var(--light-gray);
}

.esp-mp-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.esp-mp-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border-top: 4px solid var(--png-red);
}

.esp-mp-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.2);
}

.esp-mp-photo {
    width: 80px;
    height: 80px;
    background: var(--png-green);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    margin: 0 auto 1rem;
    overflow: hidden;
}

.esp-mp-photo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
    display: block;
}

.esp-mp-name {
    font-size: 1.2rem;
    color: var(--png-green);
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.esp-mp-electorate {
    color: var(--png-red);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.esp-mp-party {
    color: var(--medium-gray);
    font-size: 0.9rem;
}

/* Districts Section */
.esp-districts-section {
    padding: 2rem 0;
    background: var(--light-gray);
}

.esp-districts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.esp-district-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    border-left: 5px solid var(--png-green);
    transition: all 0.3s ease;
}

.esp-district-card:hover {
    transform: translateY(-5px);
    border-left-color: var(--png-red);
}

.esp-district-name {
    font-size: 1.4rem;
    color: var(--png-green);
    font-weight: bold;
    margin-bottom: 1rem;
}

.esp-district-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.esp-district-stat {
    text-align: center;
    padding: 0.8rem;
    background: var(--light-gray);
    border-radius: 8px;
}

.esp-district-stat-number {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--png-red);
}

.esp-district-stat-label {
    font-size: 0.8rem;
    color: var(--medium-gray);
    text-transform: uppercase;
}

.esp-district-description {
    color: #666;
    line-height: 1.6;
}

/* Statistics Section */
.esp-statistics-section {
    padding: 2rem 0;
}

.esp-map-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1.5rem;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.esp-map-stats.list {
    grid-template-columns: 1fr;
    max-width: 600px;
}

.esp-map-stat {
    background: var(--light-gray);
    padding: 1.5rem;
    border-radius: 10px;
    text-align: center;
    border-left: 4px solid var(--png-yellow);
}

.esp-map-stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: var(--png-red);
}

.esp-map-stat-label {
    color: var(--medium-gray);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Events Section */
.esp-events-section {
    padding: 2rem 0;
}

.esp-featured-events {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.esp-event-item {
    padding: 1rem 0;
    border-bottom: 1px solid #eee;
}

.esp-event-item:last-child {
    border-bottom: none;
}

.esp-event-date {
    font-size: 0.9rem;
    color: var(--png-red);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.esp-event-title {
    font-size: 1.1rem;
    color: var(--png-green);
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.esp-event-location {
    font-size: 0.9rem;
    color: var(--medium-gray);
    margin-bottom: 0.5rem;
}

.esp-event-description {
    color: #666;
    font-size: 0.95rem;
    line-height: 1.5;
}

/* News Section */
.esp-news-section {
    padding: 2rem 0;
}

.esp-featured-news {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.esp-news-item {
    padding: 1rem 0;
    border-bottom: 1px solid #eee;
}

.esp-news-item:last-child {
    border-bottom: none;
}

.esp-news-date {
    font-size: 0.9rem;
    color: var(--png-red);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.esp-news-title {
    font-size: 1.1rem;
    color: var(--png-green);
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.esp-news-source {
    font-size: 0.9rem;
    color: var(--medium-gray);
    margin-bottom: 0.5rem;
    font-style: italic;
}

.esp-news-excerpt {
    color: #666;
    font-size: 0.95rem;
    line-height: 1.5;
}

/* Contact Section */
.esp-contact-section {
    padding: 2rem 0;
    background: var(--png-green);
    color: white;
}

.esp-contact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.esp-contact-grid.list {
    grid-template-columns: 1fr;
    max-width: 600px;
}

.esp-contact-card {
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.esp-contact-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: var(--png-yellow);
}

.esp-contact-title {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: var(--png-yellow);
}

.esp-contact-info {
    line-height: 1.6;
    opacity: 0.9;
}

.esp-contact-info a {
    color: white;
    text-decoration: none;
}

.esp-contact-info a:hover {
    color: var(--png-yellow);
}

/* Slideshow Section */
.esp-slideshow-section {
    position: relative;
    height: 400px;
    overflow: hidden;
}

.esp-slideshow-container {
    position: relative;
    width: 100%;
    height: 100%;
}

.esp-slide {
    position: absolute;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 1s ease-in-out;
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
    justify-content: center;
}

.esp-slide.active {
    opacity: 1;
}

.esp-slide-1 {
    background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)), 
                linear-gradient(45deg, var(--png-green), var(--dark-green));
}

.esp-slide-2 {
    background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)), 
                linear-gradient(135deg, var(--png-red), var(--png-yellow));
}

.esp-slide-3 {
    background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)), 
                linear-gradient(90deg, var(--png-yellow), var(--png-green));
}

.esp-slide-content {
    text-align: center;
    color: white;
    max-width: 800px;
    padding: 0 2rem;
}

.esp-slide-title {
    font-size: 3rem;
    font-weight: bold;
    margin-bottom: 1rem;
    text-shadow: 3px 3px 6px rgba(0,0,0,0.5);
}

.esp-slide-subtitle {
    font-size: 1.3rem;
    opacity: 0.9;
}

.esp-slideshow-nav {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
}

.esp-slide-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: all 0.3s ease;
}

.esp-slide-dot.active {
    background: var(--png-yellow);
    transform: scale(1.2);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .esp-governor-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .esp-mp-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .esp-slide-title {
        font-size: 2rem;
    }
    
    .esp-slide-subtitle {
        font-size: 1.1rem;
    }
    
    .esp-map-stats {
        grid-template-columns: 1fr;
    }
    
    .esp-districts-grid {
        grid-template-columns: 1fr;
    }
    
    .esp-district-stats {
        grid-template-columns: 1fr;
    }
    
    .esp-contact-grid {
        grid-template-columns: 1fr;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.esp-mp-card,
.esp-district-card,
.esp-event-item,
.esp-news-item {
    animation: fadeInUp 0.6s ease-out;
}

/* Print styles */
@media print {
    .esp-slideshow-section {
        display: none;
    }
    
    .esp-contact-section {
        background: white;
        color: black;
    }
    
    .esp-contact-card {
        background: white;
        border: 1px solid #ddd;
    }
}
