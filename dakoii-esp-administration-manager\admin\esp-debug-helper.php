<?php
/**
 * ESP Debug Helper
 * 
 * Temporary debug helper to check post type registration and fix issues
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Only allow administrators to access this
if (!current_user_can('manage_options')) {
    wp_die('You do not have sufficient permissions to access this page.');
}

// Handle actions
if (isset($_GET['action'])) {
    switch ($_GET['action']) {
        case 'flush_rewrite_rules':
            flush_rewrite_rules();
            ESP_Post_Types::flush_rewrite_rules();
            add_action('admin_notices', function() {
                echo '<div class="notice notice-success is-dismissible"><p>Rewrite rules flushed successfully!</p></div>';
            });
            break;
            
        case 'check_post_types':
            $post_types = get_post_types(array(), 'objects');
            $esp_post_types = array();
            foreach ($post_types as $post_type) {
                if (strpos($post_type->name, 'esp_') === 0) {
                    $esp_post_types[] = $post_type;
                }
            }
            break;
    }
}

?>
<div class="wrap">
    <h1>ESP Administration Debug Helper</h1>
    
    <div class="card">
        <h2>Quick Fixes</h2>
        <p>Use these tools to fix common issues with the ESP Administration Manager:</p>
        
        <p>
            <a href="<?php echo admin_url('admin.php?page=esp-admin-dashboard&debug=1&action=flush_rewrite_rules'); ?>" 
               class="button button-primary">
                Flush Rewrite Rules
            </a>
            <span class="description">Fix issues with post type URLs not working</span>
        </p>
        
        <p>
            <a href="<?php echo admin_url('admin.php?page=esp-admin-dashboard&debug=1&action=check_post_types'); ?>" 
               class="button button-secondary">
                Check Post Types
            </a>
            <span class="description">Verify that all ESP post types are registered</span>
        </p>
    </div>
    
    <?php if (isset($_GET['action']) && $_GET['action'] === 'check_post_types'): ?>
    <div class="card">
        <h2>ESP Post Types Status</h2>
        <table class="widefat">
            <thead>
                <tr>
                    <th>Post Type</th>
                    <th>Label</th>
                    <th>Show UI</th>
                    <th>Show in Menu</th>
                    <th>Capabilities</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($esp_post_types as $post_type): ?>
                <tr>
                    <td><code><?php echo esc_html($post_type->name); ?></code></td>
                    <td><?php echo esc_html($post_type->labels->name); ?></td>
                    <td><?php echo $post_type->show_ui ? '✓' : '✗'; ?></td>
                    <td><?php echo $post_type->show_in_menu ? esc_html($post_type->show_in_menu) : '✗'; ?></td>
                    <td><?php echo esc_html($post_type->capability_type); ?></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
    <?php endif; ?>
    
    <div class="card">
        <h2>Test Links</h2>
        <p>Test these links to verify that the post types are working correctly:</p>
        
        <ul>
            <li><a href="<?php echo admin_url('post-new.php?post_type=esp_mp'); ?>" target="_blank">Add New MP</a></li>
            <li><a href="<?php echo admin_url('edit.php?post_type=esp_mp'); ?>" target="_blank">Manage All MPs</a></li>
            <li><a href="<?php echo admin_url('post-new.php?post_type=esp_governor'); ?>" target="_blank">Add New Governor</a></li>
            <li><a href="<?php echo admin_url('edit.php?post_type=esp_governor'); ?>" target="_blank">Manage All Governors</a></li>
            <li><a href="<?php echo admin_url('post-new.php?post_type=esp_district'); ?>" target="_blank">Add New District</a></li>
            <li><a href="<?php echo admin_url('edit.php?post_type=esp_district'); ?>" target="_blank">Manage All Districts</a></li>
        </ul>
    </div>
    
    <div class="card">
        <h2>Current User Capabilities</h2>
        <p>Your current capabilities:</p>
        <ul>
            <li>manage_options: <?php echo current_user_can('manage_options') ? '✓' : '✗'; ?></li>
            <li>edit_posts: <?php echo current_user_can('edit_posts') ? '✓' : '✗'; ?></li>
            <li>publish_posts: <?php echo current_user_can('publish_posts') ? '✓' : '✗'; ?></li>
        </ul>
    </div>
</div>

<style>
.card {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin: 20px 0;
}

.card h2 {
    margin-top: 0;
}

.description {
    font-style: italic;
    color: #666;
    margin-left: 10px;
}
</style>
